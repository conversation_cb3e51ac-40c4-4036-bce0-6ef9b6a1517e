#!/usr/bin/env python3
"""
模型对比脚本
比较原始模型和边界感知模型的结果
"""

import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.gridspec import GridSpec

from config import *
from dataset import MultiViewHSIDataset
from model import IntrinsicDecompositionNet


def load_model(model_path, input_channels, output_channels):
    """加载模型"""
    model = IntrinsicDecompositionNet(input_channels, output_channels)
    checkpoint = torch.load(model_path, map_location='cpu')
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    return model


def analyze_boundary_improvement(original_model_path, boundary_model_path):
    """分析边界改善效果"""
    print("🔍 模型对比分析")
    print("=" * 50)
    
    # 创建数据集
    dataset = MultiViewHSIDataset(DATA_DIR, ANGLES)
    input_channels = dataset.get_input_channels()
    output_channels = dataset.get_output_channels()
    
    # 加载模型
    print("📥 加载模型...")
    original_model = load_model(original_model_path, input_channels, output_channels)
    boundary_model = load_model(boundary_model_path, input_channels, output_channels)
    
    # 获取测试样本
    sample = dataset[0]
    
    with torch.no_grad():
        # 原始模型预测
        input1 = sample['input1'].unsqueeze(0)
        input2 = sample['input2'].unsqueeze(0)
        
        log_r1_orig, log_s1_orig = original_model(input1)
        log_r2_orig, log_s2_orig = original_model(input2)
        
        # 边界感知模型预测
        log_r1_bound, log_s1_bound = boundary_model(input1)
        log_r2_bound, log_s2_bound = boundary_model(input2)
        
        # 转换为线性域
        r1_orig = torch.exp(log_r1_orig).squeeze(0).permute(1, 2, 0).numpy()
        r2_orig = torch.exp(log_r2_orig).squeeze(0).permute(1, 2, 0).numpy()
        r1_bound = torch.exp(log_r1_bound).squeeze(0).permute(1, 2, 0).numpy()
        r2_bound = torch.exp(log_r2_bound).squeeze(0).permute(1, 2, 0).numpy()
        
        # 原始HSI
        hsi1 = sample['hsi1'].permute(1, 2, 0).numpy()
        hsi2 = sample['hsi2'].permute(1, 2, 0).numpy()
    
    # 分析边界效应
    H, W, C = r1_orig.shape
    edge_width = max(1, min(H//10, W//10))
    
    # 创建掩码
    edge_mask = np.zeros((H, W), dtype=bool)
    edge_mask[:edge_width, :] = True
    edge_mask[-edge_width:, :] = True
    edge_mask[:, :edge_width] = True
    edge_mask[:, -edge_width:] = True
    
    center_mask = np.zeros((H, W), dtype=bool)
    center_mask[edge_width:-edge_width, edge_width:-edge_width] = True
    
    # 计算统计信息
    print(f"\n📊 边界效应分析 (图像尺寸: {H}×{W}, 边界宽度: {edge_width})")
    
    def analyze_region(data, name):
        edge_mean = np.mean(data[edge_mask])
        center_mean = np.mean(data[center_mask])
        ratio = center_mean / edge_mean if edge_mean != 0 else float('inf')
        print(f"  {name}:")
        print(f"    边界均值: {edge_mean:.4f}, 中心均值: {center_mean:.4f}")
        print(f"    中心/边界比值: {ratio:.3f}")
        return ratio
    
    # 分析各个数据
    print("\n🔸 原始HSI数据:")
    hsi1_ratio = analyze_region(hsi1, "HSI1")
    hsi2_ratio = analyze_region(hsi2, "HSI2")
    
    print("\n🔸 原始模型预测:")
    orig1_ratio = analyze_region(r1_orig, "反射率1")
    orig2_ratio = analyze_region(r2_orig, "反射率2")
    
    print("\n🔸 边界感知模型预测:")
    bound1_ratio = analyze_region(r1_bound, "反射率1")
    bound2_ratio = analyze_region(r2_bound, "反射率2")
    
    # 计算改善程度
    print("\n📈 改善效果:")
    improvement1 = abs(hsi1_ratio - bound1_ratio) / abs(hsi1_ratio - orig1_ratio) if abs(hsi1_ratio - orig1_ratio) > 0 else 0
    improvement2 = abs(hsi2_ratio - bound2_ratio) / abs(hsi2_ratio - orig2_ratio) if abs(hsi2_ratio - orig2_ratio) > 0 else 0
    
    print(f"  反射率1 改善程度: {improvement1:.1%}")
    print(f"  反射率2 改善程度: {improvement2:.1%}")
    
    # 可视化对比
    fig = plt.figure(figsize=(20, 16))
    gs = GridSpec(4, 5, figure=fig, hspace=0.3, wspace=0.3)
    
    # 选择代表性波段
    band_idx = C // 2
    
    # 第一行：原始HSI
    ax1 = fig.add_subplot(gs[0, 0])
    im1 = ax1.imshow(hsi1[:, :, band_idx], cmap='viridis')
    ax1.set_title(f'原始HSI1 - Band {band_idx}')
    ax1.axis('off')
    plt.colorbar(im1, ax=ax1, fraction=0.046, pad=0.04)
    
    ax2 = fig.add_subplot(gs[0, 1])
    im2 = ax2.imshow(hsi2[:, :, band_idx], cmap='viridis')
    ax2.set_title(f'原始HSI2 - Band {band_idx}')
    ax2.axis('off')
    plt.colorbar(im2, ax=ax2, fraction=0.046, pad=0.04)
    
    # 第二行：原始模型预测
    ax3 = fig.add_subplot(gs[1, 0])
    im3 = ax3.imshow(r1_orig[:, :, band_idx], cmap='viridis')
    ax3.set_title(f'原始模型 R1 - Band {band_idx}')
    ax3.axis('off')
    plt.colorbar(im3, ax=ax3, fraction=0.046, pad=0.04)
    
    ax4 = fig.add_subplot(gs[1, 1])
    im4 = ax4.imshow(r2_orig[:, :, band_idx], cmap='viridis')
    ax4.set_title(f'原始模型 R2 - Band {band_idx}')
    ax4.axis('off')
    plt.colorbar(im4, ax=ax4, fraction=0.046, pad=0.04)
    
    # 第三行：边界感知模型预测
    ax5 = fig.add_subplot(gs[2, 0])
    im5 = ax5.imshow(r1_bound[:, :, band_idx], cmap='viridis')
    ax5.set_title(f'边界感知模型 R1 - Band {band_idx}')
    ax5.axis('off')
    plt.colorbar(im5, ax=ax5, fraction=0.046, pad=0.04)
    
    ax6 = fig.add_subplot(gs[2, 1])
    im6 = ax6.imshow(r2_bound[:, :, band_idx], cmap='viridis')
    ax6.set_title(f'边界感知模型 R2 - Band {band_idx}')
    ax6.axis('off')
    plt.colorbar(im6, ax=ax6, fraction=0.046, pad=0.04)
    
    # 第四行：差异图
    diff_orig1 = np.abs(hsi1[:, :, band_idx] - r1_orig[:, :, band_idx])
    diff_bound1 = np.abs(hsi1[:, :, band_idx] - r1_bound[:, :, band_idx])
    
    ax7 = fig.add_subplot(gs[3, 0])
    im7 = ax7.imshow(diff_orig1, cmap='hot')
    ax7.set_title('原始模型 |HSI1-R1| 差异')
    ax7.axis('off')
    plt.colorbar(im7, ax=ax7, fraction=0.046, pad=0.04)
    
    ax8 = fig.add_subplot(gs[3, 1])
    im8 = ax8.imshow(diff_bound1, cmap='hot')
    ax8.set_title('边界感知模型 |HSI1-R1| 差异')
    ax8.axis('off')
    plt.colorbar(im8, ax=ax8, fraction=0.046, pad=0.04)
    
    # 掩码可视化
    ax9 = fig.add_subplot(gs[0, 2])
    ax9.imshow(edge_mask.astype(float), cmap='gray')
    ax9.set_title('边界区域掩码')
    ax9.axis('off')
    
    ax10 = fig.add_subplot(gs[1, 2])
    ax10.imshow(center_mask.astype(float), cmap='gray')
    ax10.set_title('中心区域掩码')
    ax10.axis('off')
    
    # 统计信息文本
    ax11 = fig.add_subplot(gs[2, 2])
    ax11.axis('off')
    stats_text = f"""边界效应统计:

原始HSI比值:
  HSI1: {hsi1_ratio:.3f}
  HSI2: {hsi2_ratio:.3f}

原始模型比值:
  R1: {orig1_ratio:.3f}
  R2: {orig2_ratio:.3f}

边界感知模型比值:
  R1: {bound1_ratio:.3f}
  R2: {bound2_ratio:.3f}

改善程度:
  R1: {improvement1:.1%}
  R2: {improvement2:.1%}"""
    
    ax11.text(0.05, 0.95, stats_text, transform=ax11.transAxes,
             verticalalignment='top', fontsize=10, fontfamily='monospace',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8))
    
    # 光谱曲线对比
    ax12 = fig.add_subplot(gs[:2, 3:])
    
    # 选择中心区域的一个像素进行光谱对比
    center_h, center_w = H//2, W//2
    wavelengths = np.linspace(400, 1000, C)
    
    ax12.plot(wavelengths, hsi1[center_h, center_w, :], 'k-', linewidth=2, label='原始HSI1', alpha=0.8)
    ax12.plot(wavelengths, r1_orig[center_h, center_w, :], 'r--', linewidth=2, label='原始模型R1', alpha=0.8)
    ax12.plot(wavelengths, r1_bound[center_h, center_w, :], 'b-', linewidth=2, label='边界感知模型R1', alpha=0.8)
    
    ax12.set_xlabel('波长 (nm)')
    ax12.set_ylabel('反射率')
    ax12.set_title(f'中心像素({center_h},{center_w})光谱对比')
    ax12.legend()
    ax12.grid(True, alpha=0.3)
    
    # 边界像素光谱对比
    ax13 = fig.add_subplot(gs[2:, 3:])
    
    # 选择边界区域的一个像素
    edge_h, edge_w = edge_width//2, edge_width//2
    
    ax13.plot(wavelengths, hsi1[edge_h, edge_w, :], 'k-', linewidth=2, label='原始HSI1', alpha=0.8)
    ax13.plot(wavelengths, r1_orig[edge_h, edge_w, :], 'r--', linewidth=2, label='原始模型R1', alpha=0.8)
    ax13.plot(wavelengths, r1_bound[edge_h, edge_w, :], 'b-', linewidth=2, label='边界感知模型R1', alpha=0.8)
    
    ax13.set_xlabel('波长 (nm)')
    ax13.set_ylabel('反射率')
    ax13.set_title(f'边界像素({edge_h},{edge_w})光谱对比')
    ax13.legend()
    ax13.grid(True, alpha=0.3)
    
    plt.savefig('model_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n✅ 对比分析完成！")
    print(f"可视化结果保存为: model_comparison.png")
    
    return {
        'original_ratios': [orig1_ratio, orig2_ratio],
        'boundary_ratios': [bound1_ratio, bound2_ratio],
        'improvements': [improvement1, improvement2]
    }


def main():
    """主函数"""
    # 模型路径
    original_model_path = "saved_models/best_model.pth"
    
    # 查找边界感知模型
    boundary_model_path = None
    for item in os.listdir('.'):
        if item.startswith('boundary_aware_training_'):
            model_file = os.path.join(item, 'best_boundary_aware_model.pth')
            if os.path.exists(model_file):
                boundary_model_path = model_file
                break
    
    if not boundary_model_path:
        print("❌ 找不到边界感知模型，请先运行 train_boundary_aware.py")
        return
    
    print(f"📁 原始模型: {original_model_path}")
    print(f"📁 边界感知模型: {boundary_model_path}")
    
    # 执行对比分析
    results = analyze_boundary_improvement(original_model_path, boundary_model_path)
    
    # 总结
    avg_improvement = np.mean(results['improvements'])
    print(f"\n🎯 总体改善效果: {avg_improvement:.1%}")
    
    if avg_improvement > 0.1:  # 10%以上改善
        print("✅ 边界感知训练显著改善了边界效应问题！")
    elif avg_improvement > 0.05:  # 5%以上改善
        print("✅ 边界感知训练有效改善了边界效应问题！")
    else:
        print("⚠️ 边界感知训练的改善效果有限，可能需要进一步调整参数。")


if __name__ == "__main__":
    main()

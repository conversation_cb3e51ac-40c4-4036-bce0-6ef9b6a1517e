#!/usr/bin/env python3
"""
边界感知的本征分解网络架构
解决传统U-Net在边界区域重建质量差的问题
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, List


class EdgeAwareConv(nn.Module):
    """边界感知卷积模块"""
    
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int = 3):
        super(EdgeAwareConv, self).__init__()
        
        # 标准卷积
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, padding=kernel_size//2)
        
        # 边界检测卷积 (Sobel算子)
        self.edge_conv_x = nn.Conv2d(in_channels, out_channels, 3, padding=1, bias=False)
        self.edge_conv_y = nn.Conv2d(in_channels, out_channels, 3, padding=1, bias=False)
        
        # 初始化边界检测卷积核
        self._init_edge_kernels()
        
        # 特征融合
        self.fusion = nn.Conv2d(out_channels * 3, out_channels, 1)
        self.bn = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)
    
    def _init_edge_kernels(self):
        """初始化边界检测卷积核"""
        # Sobel X 算子
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32)
        # Sobel Y 算子  
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32)
        
        # 扩展到所有输入输出通道
        in_ch = self.edge_conv_x.in_channels
        out_ch = self.edge_conv_x.out_channels
        
        sobel_x = sobel_x.unsqueeze(0).unsqueeze(0).repeat(out_ch, in_ch, 1, 1)
        sobel_y = sobel_y.unsqueeze(0).unsqueeze(0).repeat(out_ch, in_ch, 1, 1)
        
        self.edge_conv_x.weight.data = sobel_x
        self.edge_conv_y.weight.data = sobel_y
        
        # 冻结边界检测卷积核
        self.edge_conv_x.weight.requires_grad = False
        self.edge_conv_y.weight.requires_grad = False
    
    def forward(self, x):
        # 标准卷积特征
        conv_feat = self.conv(x)
        
        # 边界特征
        edge_x = self.edge_conv_x(x)
        edge_y = self.edge_conv_y(x)
        
        # 融合所有特征
        combined = torch.cat([conv_feat, edge_x, edge_y], dim=1)
        out = self.fusion(combined)
        out = self.bn(out)
        out = self.relu(out)
        
        return out


class MultiScaleBlock(nn.Module):
    """多尺度特征提取模块"""
    
    def __init__(self, in_channels: int, out_channels: int):
        super(MultiScaleBlock, self).__init__()
        
        # 不同尺度的卷积
        self.conv1x1 = nn.Conv2d(in_channels, out_channels//4, 1)
        self.conv3x3 = nn.Conv2d(in_channels, out_channels//4, 3, padding=1)
        self.conv5x5 = nn.Conv2d(in_channels, out_channels//4, 5, padding=2)
        self.conv7x7 = nn.Conv2d(in_channels, out_channels//4, 7, padding=3)
        
        self.bn = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)
    
    def forward(self, x):
        feat1 = self.conv1x1(x)
        feat3 = self.conv3x3(x)
        feat5 = self.conv5x5(x)
        feat7 = self.conv7x7(x)
        
        out = torch.cat([feat1, feat3, feat5, feat7], dim=1)
        out = self.bn(out)
        out = self.relu(out)
        
        return out


class BoundaryPreservingUNet(nn.Module):
    """边界保持的U-Net架构"""
    
    def __init__(self, in_channels: int, out_channels: int, base_channels: int = 64):
        super(BoundaryPreservingUNet, self).__init__()
        
        # 编码器 - 使用边界感知卷积
        self.inc = EdgeAwareConv(in_channels, base_channels)
        self.down1 = self._make_down_layer(base_channels, base_channels * 2)
        self.down2 = self._make_down_layer(base_channels * 2, base_channels * 4)
        self.down3 = self._make_down_layer(base_channels * 4, base_channels * 8)
        
        # 瓶颈层 - 多尺度特征提取
        self.bottleneck = MultiScaleBlock(base_channels * 8, base_channels * 16)
        
        # 解码器 - 保持高分辨率路径
        self.up1 = self._make_up_layer(base_channels * 16, base_channels * 8)
        self.up2 = self._make_up_layer(base_channels * 8, base_channels * 4)
        self.up3 = self._make_up_layer(base_channels * 4, base_channels * 2)
        self.up4 = self._make_up_layer(base_channels * 2, base_channels)
        
        # 高分辨率路径 - 保持原始分辨率
        self.high_res_path = nn.Sequential(
            EdgeAwareConv(in_channels, base_channels//2),
            EdgeAwareConv(base_channels//2, base_channels//2),
            EdgeAwareConv(base_channels//2, base_channels//2)
        )
        
        # 最终融合
        self.final_conv = nn.Sequential(
            EdgeAwareConv(base_channels + base_channels//2, base_channels),
            nn.Conv2d(base_channels, out_channels, 1)
        )
    
    def _make_down_layer(self, in_ch: int, out_ch: int):
        return nn.Sequential(
            nn.MaxPool2d(2),
            EdgeAwareConv(in_ch, out_ch)
        )
    
    def _make_up_layer(self, in_ch: int, out_ch: int):
        return nn.Sequential(
            nn.ConvTranspose2d(in_ch, in_ch//2, 2, stride=2),
            EdgeAwareConv(in_ch//2 + out_ch, out_ch)  # 修正skip connection的通道数
        )
    
    def forward(self, x):
        # 高分辨率路径
        high_res = self.high_res_path(x)
        
        # 编码器路径
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)
        
        # 瓶颈层
        x5 = self.bottleneck(x4)
        
        # 解码器路径
        x = self.up1(torch.cat([F.interpolate(x5, x4.shape[2:]), x4], dim=1))
        x = self.up2(torch.cat([F.interpolate(x, x3.shape[2:]), x3], dim=1))
        x = self.up3(torch.cat([F.interpolate(x, x2.shape[2:]), x2], dim=1))
        x = self.up4(torch.cat([F.interpolate(x, x1.shape[2:]), x1], dim=1))
        
        # 融合高分辨率路径
        x = torch.cat([x, high_res], dim=1)
        
        # 最终输出
        out = self.final_conv(x)
        
        return out


class BoundaryAwareIntrinsicNet(nn.Module):
    """边界感知的本征分解网络"""
    
    def __init__(self, input_channels: int, output_channels: int, base_channels: int = 64):
        super(BoundaryAwareIntrinsicNet, self).__init__()
        
        # 共享特征提取器
        self.shared_encoder = nn.Sequential(
            EdgeAwareConv(input_channels, base_channels),
            EdgeAwareConv(base_channels, base_channels),
        )
        
        # 反射率分支
        self.reflectance_net = BoundaryPreservingUNet(
            base_channels, output_channels, base_channels
        )
        
        # 阴影分支  
        self.shading_net = BoundaryPreservingUNet(
            base_channels, output_channels, base_channels
        )
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        # 共享特征提取
        shared_feat = self.shared_encoder(x)
        
        # 分别估计反射率和阴影
        log_reflectance = self.reflectance_net(shared_feat)
        log_shading = self.shading_net(shared_feat)
        
        return log_reflectance, log_shading


def create_boundary_aware_model(input_channels: int, 
                               output_channels: int,
                               base_channels: int = 64,
                               device: torch.device = None) -> BoundaryAwareIntrinsicNet:
    """创建边界感知模型"""
    
    model = BoundaryAwareIntrinsicNet(
        input_channels=input_channels,
        output_channels=output_channels, 
        base_channels=base_channels
    )
    
    if device is not None:
        model = model.to(device)
    
    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"边界感知模型创建完成:")
    print(f"  输入通道数: {input_channels}")
    print(f"  输出通道数: {output_channels}")
    print(f"  基础通道数: {base_channels}")
    print(f"  总参数数: {total_params:,}")
    print(f"  可训练参数数: {trainable_params:,}")
    print(f"  计算设备: {device}")
    
    return model

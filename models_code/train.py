# -*- coding: utf-8 -*-
"""
训练脚本 - 基于多模态数据与物理先验的苹果本征高光谱特性提取模型
"""

import os
import time
import torch
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter
import numpy as np
from tqdm import tqdm
import matplotlib.pyplot as plt
from typing import Tuple, Dict

from config import *
from dataset import create_data_loaders
from model import create_model
from loss import create_loss_function
from utils import visualize_spectrum, create_visualization_grid
from visualizer import create_visualizer


class Trainer:
    """训练器类"""
    
    def __init__(self):
        # 设置设备
        self.device = get_device()
        
        # 创建必要目录
        create_directories()
        
        # 创建数据加载器
        print("创建数据加载器...")
        self.train_loader, self.val_loader = create_data_loaders(
            data_dir=DATA_DIR,
            angles=ANGLES,
            batch_size=BATCH_SIZE,
            val_ratio=VAL_RATIO,
            num_workers=4
        )
        
        # 获取输入输出通道数
        sample_dataset = self.train_loader.dataset.dataset
        self.input_channels = sample_dataset.get_input_channels()
        self.output_channels = sample_dataset.get_output_channels()

        # 更新全局配置中的图像尺寸
        global IMG_HEIGHT, IMG_WIDTH
        IMG_HEIGHT = sample_dataset.H
        IMG_WIDTH = sample_dataset.W
        print(f"图像尺寸已确定: {IMG_HEIGHT} × {IMG_WIDTH}")
        
        # 创建模型
        print("创建模型...")
        self.model = create_model(
            input_channels=self.input_channels,
            output_channels=self.output_channels,
            base_channels=BASE_CHANNELS,
            use_batch_norm=USE_BATCH_NORM,
            device=self.device
        )
        
        # 创建损失函数
        print("创建损失函数...")
        self.criterion = create_loss_function(
            lambda_rec=LAMBDA_REC,
            lambda_r_smooth_2d=LAMBDA_R_SMOOTH_2D,
            lambda_s_normal=LAMBDA_S_NORMAL,
            lambda_r_const=LAMBDA_R_CONST,
            shading_normal_alpha=SHADING_NORMAL_ALPHA
        )
        
        # 创建优化器
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=LEARNING_RATE,
            weight_decay=1e-5
        )
        
        # 创建学习率调度器
        if LR_SCHEDULER == "cosine":
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer, T_max=NUM_EPOCHS
            )
        elif LR_SCHEDULER == "step":
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer, step_size=30, gamma=0.1
            )
        else:
            self.scheduler = None
        
        # 创建TensorBoard记录器
        self.writer = SummaryWriter(LOG_DIR)

        # 创建可视化器
        self.visualizer = create_visualizer()

        # 训练状态
        self.current_epoch = 0
        self.best_val_loss = float('inf')
        self.train_losses = []
        self.val_losses = []
        self.loss_details = {
            'reconstruction_loss': [],
            'smoothness_loss': [],
            'normal_consistency_loss': [],
            'reflectance_consistency_loss': []
        }
        
        print("训练器初始化完成!")
    
    def train_epoch(self) -> Tuple[float, Dict[str, float]]:
        """训练一个epoch"""
        self.model.train()
        epoch_losses = []
        epoch_loss_details = {
            'reconstruction_loss': [],
            'smoothness_loss': [],
            'normal_consistency_loss': [],
            'reflectance_consistency_loss': []
        }
        
        pbar = tqdm(self.train_loader, desc=f"Epoch {self.current_epoch+1}/{NUM_EPOCHS}")
        
        for batch_idx, batch in enumerate(pbar):
            # 将数据移到设备
            batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                    for k, v in batch.items()}
            
            # 前向传播
            self.optimizer.zero_grad()
            
            # 获取两个视角的输入
            input1 = batch['input1']  # (B, C, H, W)
            input2 = batch['input2']  # (B, C, H, W)
            
            # 提取对数域HSI数据
            log_hsi1 = batch['log_hsi1']  # 对数域HSI数据
            log_hsi2 = batch['log_hsi2']  # 对数域HSI数据
            
            # 网络预测
            log_reflectance1, log_shading1 = self.model(input1)
            log_reflectance2, log_shading2 = self.model(input2)
            
            # 计算损失
            total_loss, loss_dict = self.criterion(
                log_hsi1, log_hsi2,
                log_reflectance1, log_reflectance2,
                log_shading1, log_shading2,
                batch['normal1'], batch['normal2']
            )
            
            # 反向传播
            total_loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), GRAD_CLIP_THRESHOLD)
            
            self.optimizer.step()
            
            # 记录损失
            epoch_losses.append(total_loss.item())
            for key in epoch_loss_details:
                if key in loss_dict:
                    epoch_loss_details[key].append(loss_dict[key])
            
            # 更新进度条
            pbar.set_postfix({
                'Loss': f"{total_loss.item():.4f}",
                'Rec': f"{loss_dict.get('reconstruction_loss', 0):.4f}",
                'Smooth': f"{loss_dict.get('smoothness_loss', 0):.4f}",
                'Normal': f"{loss_dict.get('normal_consistency_loss', 0):.4f}",
                'Const': f"{loss_dict.get('reflectance_consistency_loss', 0):.4f}"
            })
            
            # 记录到TensorBoard
            if batch_idx % LOG_FREQUENCY == 0:
                global_step = self.current_epoch * len(self.train_loader) + batch_idx
                self.writer.add_scalar('Train/Total_Loss', total_loss.item(), global_step)
                for key, value in loss_dict.items():
                    self.writer.add_scalar(f'Train/{key}', value, global_step)

            # 可视化分解结果（每个epoch的第一个batch）
            if batch_idx == 0 and (self.current_epoch + 1) % VIS_FREQUENCY == 0:
                with torch.no_grad():  # 确保在无梯度上下文中进行可视化
                    self.visualizer.visualize_decomposition_results(
                        batch, log_reflectance1, log_shading1,
                        self.current_epoch + 1, batch_idx
                    )
        
        # 计算平均损失
        avg_loss = np.mean(epoch_losses)
        avg_loss_details = {key: np.mean(values) for key, values in epoch_loss_details.items()}
        
        return avg_loss, avg_loss_details
    
    def validate_epoch(self) -> float:
        """验证一个epoch"""
        self.model.eval()
        epoch_losses = []
        epoch_loss_details = {
            'reconstruction_loss': [],
            'smoothness_loss': [],
            'normal_consistency_loss': [],
            'reflectance_consistency_loss': []
        }
        
        with torch.no_grad():
            for batch in tqdm(self.val_loader, desc="Validation"):
                # 将数据移到设备
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                        for k, v in batch.items()}
                
                # 前向传播
                input1 = batch['input1']
                input2 = batch['input2']
                log_hsi1 = batch['log_hsi1']
                log_hsi2 = batch['log_hsi2']
                
                log_reflectance1, log_shading1 = self.model(input1)
                log_reflectance2, log_shading2 = self.model(input2)
                
                # 计算损失
                total_loss, loss_dict = self.criterion(
                    log_hsi1, log_hsi2,
                    log_reflectance1, log_reflectance2,
                    log_shading1, log_shading2,
                    batch['normal1'], batch['normal2']
                )
                
                # 记录损失
                epoch_losses.append(total_loss.item())
                for key in epoch_loss_details:
                    if key in loss_dict:
                        epoch_loss_details[key].append(loss_dict[key])
        
        # 计算平均损失
        avg_loss = np.mean(epoch_losses)
        avg_loss_details = {key: np.mean(values) for key, values in epoch_loss_details.items()}
        
        return avg_loss, avg_loss_details
    
    def save_checkpoint(self, is_best: bool = False):
        """保存检查点"""
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'best_val_loss': self.best_val_loss,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses
        }
        
        # 保存最新检查点
        checkpoint_path = os.path.join(SAVE_DIR, 'latest_checkpoint.pth')
        torch.save(checkpoint, checkpoint_path)
        
        # 保存最佳模型
        if is_best:
            best_path = os.path.join(SAVE_DIR, 'best_model.pth')
            torch.save(checkpoint, best_path)
            print(f"保存最佳模型到: {best_path}")
        
        # 定期保存
        if (self.current_epoch + 1) % SAVE_FREQUENCY == 0:
            epoch_path = os.path.join(SAVE_DIR, f'checkpoint_epoch_{self.current_epoch+1}.pth')
            torch.save(checkpoint, epoch_path)
    
    def load_checkpoint(self, checkpoint_path: str):
        """加载检查点"""
        if os.path.exists(checkpoint_path):
            checkpoint = torch.load(checkpoint_path, map_location=self.device)
            
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            
            if self.scheduler and checkpoint['scheduler_state_dict']:
                self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            
            self.current_epoch = checkpoint['epoch']
            self.best_val_loss = checkpoint['best_val_loss']
            self.train_losses = checkpoint['train_losses']
            self.val_losses = checkpoint['val_losses']
            
            print(f"从检查点恢复训练: epoch {self.current_epoch+1}")
        else:
            print(f"检查点文件不存在: {checkpoint_path}")
    
    def train(self):
        """主训练循环"""
        print("开始训练...")
        print_config()
        
        start_time = time.time()
        
        for epoch in range(self.current_epoch, NUM_EPOCHS):
            self.current_epoch = epoch
            
            # 训练
            train_loss, train_loss_details = self.train_epoch()
            self.train_losses.append(train_loss)

            # 更新详细损失历史
            for key, value in train_loss_details.items():
                if key in self.loss_details:
                    self.loss_details[key].append(value)
            
            # 验证
            if VALIDATE_DURING_TRAINING and (epoch + 1) % VAL_FREQUENCY == 0:
                val_loss, val_loss_details = self.validate_epoch()
                self.val_losses.append(val_loss)
                
                # 记录到TensorBoard
                self.writer.add_scalar('Val/Total_Loss', val_loss, epoch)
                for key, value in val_loss_details.items():
                    self.writer.add_scalar(f'Val/{key}', value, epoch)
                
                # 检查是否是最佳模型
                is_best = val_loss < self.best_val_loss
                if is_best:
                    self.best_val_loss = val_loss
                
                print(f"Epoch {epoch+1}/{NUM_EPOCHS} - "
                      f"Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")

                # 可视化损失曲线
                if (epoch + 1) % VIS_FREQUENCY == 0:
                    self.visualizer.visualize_loss_curves(
                        self.train_losses, self.val_losses, self.loss_details, epoch + 1
                    )

                    # 创建训练摘要
                    current_lr = self.scheduler.get_last_lr()[0] if self.scheduler else LEARNING_RATE
                    self.visualizer.create_training_summary(
                        epoch + 1, train_loss, val_loss, self.best_val_loss, current_lr
                    )

                # 保存检查点
                self.save_checkpoint(is_best)
            else:
                print(f"Epoch {epoch+1}/{NUM_EPOCHS} - Train Loss: {train_loss:.4f}")
                self.save_checkpoint()
            
            # 更新学习率
            if self.scheduler:
                self.scheduler.step()
                current_lr = self.scheduler.get_last_lr()[0]
                self.writer.add_scalar('Train/Learning_Rate', current_lr, epoch)
        
        # 训练完成
        total_time = time.time() - start_time
        print(f"训练完成! 总用时: {total_time/3600:.2f} 小时")
        
        # 关闭TensorBoard
        self.writer.close()


def main():
    """主函数"""
    # 验证配置
    validate_config()
    
    # 创建训练器
    trainer = Trainer()
    
    # 检查是否有已保存的检查点
    latest_checkpoint = os.path.join(SAVE_DIR, 'latest_checkpoint.pth')
    if os.path.exists(latest_checkpoint):
        print(f"发现检查点文件: {latest_checkpoint}")
        response = input("是否从检查点恢复训练? (y/n): ")
        if response.lower() == 'y':
            trainer.load_checkpoint(latest_checkpoint)
    
    # 开始训练
    trainer.train()


if __name__ == "__main__":
    main()

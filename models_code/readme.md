好的，我将综合我们讨论的所有细节，为您生成一个完整的、可以直接提供给 Cursor (或其他类似AI代码助手) 的项目提示词。这将是一个包含了所有阶段和具体实现的详细指令。

---

**【Cursor AI 代码生成项目提示词】**

**项目名称：** 基于多模态数据与物理先验的苹果本征高光谱特性提取与畸变分析模型

**项目目标：**
请使用 Python 和 PyTorch 实现一个深度学习模型，用于从苹果的多视角高光谱图像和对应的三维点云数据中，分解出苹果的本征反射率 (光照不变的“真实颜色”) 和光照/阴影分量。此模型的灵感来源于提供的HTML流程图。

**代码生成主路径：**
`/Users/<USER>/Desktop/研究生学业/高光谱原理解析/528/models_code`
请在该路径下创建以下文件结构，并填充代码：

```
models_code/
├── config.py            # 配置文件 (路径、超参数)
├── utils.py             # 辅助函数 (数据加载、特征图创建、法向量计算等)
├── data_loader.py       # PyTorch Dataset 和 DataLoader
├── model.py             # RES (反射率估计) 和 SES (光照估计) 网络定义
├── losses.py            # 自定义损失函数
├── train.py             # 主训练脚本
```

**数据说明：**
1. 空间位置信息 (5列)
row (第1列): 高光谱图像中的行坐标
col (第2列): 高光谱图像中的列坐标
x (第3列): 3D点云X坐标 (单位: mm)
y (第4列): 3D点云Y坐标 (单位: mm)
z (第5列): 3D点云Z坐标 (单位: mm)
2. 法向量信息 (6列) - 核心改进内容
normal_x (第6列): 方向一致的法向量X分量 [-1, 1]
normal_y (第7列): 方向一致的法向量Y分量 [-1, 1]
normal_z (第8列): 方向一致的法向量Z分量 [0.246, 1.000] ⭐已消除负值
normal_angle_with_z (第9列): 法向量与Z轴夹角 (度) [0°, 90°]
normal_orientation_xy (第10列): 法向量在XY平面投影的方向角 (弧度) [-π, π]
normal_inclination (第11列): 法向量倾斜角 (度) [0°, 90°]

**核心依赖库：**
*   `torch` (>=1.8), `torchvision`
*   `pandas`
*   `numpy`
*   `open3d` (用于法向量估计)
*   `scikit-learn` (可选, 用于某些辅助计算)
*   `matplotlib` / `PIL` (用于可视化)

---

**详细实现步骤：**

**I. 配置文件 (`config.py`)**

请创建 `config.py` 文件，并包含以下可配置参数：

```python
# 数据路径
DATA_DIR = "/Users/<USER>/Desktop/研究生学业/高光谱原理解析/528/fusion_data/visualization/"
ANGLES = [35, 45, 55, 65, 70] # 示例角度，根据实际数据调整

# 图像与数据维度 (IMG_HEIGHT 和 IMG_WIDTH 将在首次加载数据时动态确定并更新)
IMG_HEIGHT = None
IMG_WIDTH = None
NUM_BANDS = 224
NUM_POINT_CLOUD_COORDS = 3
NUM_NORMAL_COORDS = 3

# 训练超参数
BATCH_SIZE = 2 # 由于每个样本包含一对视角，实际处理的图像是 BATCH_SIZE * 2
LEARNING_RATE = 1e-4
NUM_EPOCHS = 100
DEVICE = "cuda" # 或者 "mps" for Apple Silicon, "cpu"

# 损失函数权重
LAMBDA_REC = 1.0
LAMBDA_R_SMOOTH_2D = 0.1
LAMBDA_S_NORMAL = 0.1
LAMBDA_R_CONST = 0.5

# 其他参数
LOG_EPSILON = 1e-6
R_INIT_ROI_RATIO = 0.2 # 用于计算 R_init 时中心区域的比例

# Open3D法向量估计参数
NORMAL_EST_RADIUS = 0.005 # 假设点云单位是米，苹果半径约0.04m，此值为半径的1/8左右
NORMAL_EST_MAX_NN = 30

# Shading-Normal Loss 参数
SHADING_NORMAL_ALPHA = 1.0 # L_S_normal 中 exp(-alpha * grad_N_proxy) 的 alpha
```

**II. 辅助函数 (`utils.py`)**

1.  **`get_image_dims_from_csv(csv_file_path: str) -> tuple[int, int]`**:
    *   读取指定的CSV文件。
    *   从 'u' 和 'v' 列找到最大值。
    *   返回 `(max_v + 1, max_u + 1)` 作为图像的高度 `H` 和宽度 `W`。

2.  **`load_raw_data_from_csv(csv_file_path: str) -> tuple[np.ndarray, np.ndarray, np.ndarray]`**:
    *   使用 `pandas` 读取CSV。
    *   提取像素坐标 `(u,v)` 为 `pixel_coords` (N, 2) NumPy 数组。
    *   提取点云坐标 `(x,y,z)` 为 `point_cloud_data_flat` (N, 3) NumPy 数组。
    *   提取高光谱数据 `(b1, ..., b224)` 为 `hsi_data_flat` (N, NUM_BANDS) NumPy 数组。
    *   返回 `pixel_coords, point_cloud_data_flat, hsi_data_flat`。

3.  **`create_map_from_points(pixel_coords: np.ndarray, data_values: np.ndarray, H: int, W: int, num_channels: int, default_val: float = 0.0) -> np.ndarray`**:
    *   创建一个形状为 `(H, W, num_channels)` 的 NumPy 数组，用 `default_val` 初始化。
    *   使用 `pixel_coords` (包含u,v) 作为索引，将 `data_values` (N, num_channels) 填充到该数组中。 `image[v, u, :] = data_value_for_pixel_uv`。
    *   返回填充后的特征图。

4.  **`calculate_normals_open3d(point_cloud_data_flat: np.ndarray, radius: float, max_nn: int) -> np.ndarray`**:
    *   将 `point_cloud_data_flat` (N,3) 转换为 `open3d.geometry.PointCloud`。
    *   使用 `pcd.estimate_normals(search_param=o3d.geometry.KDTreeSearchParamHybrid(radius=radius, max_nn=max_nn))` 计算法向量。
    *   可选：使用 `pcd.orient_normals_consistent_tangent_plane(k=...)` 或 `pcd.orient_normals_to_align_with_direction(orientation_reference=...)` 统一法向量方向 (例如，都指向外侧，可能需要一个参考点如物体中心或相机位置)。如果难以实现，可以先跳过方向统一，但要注意其对 `L_S_normal` 的影响。
    *   返回 (N,3) 的法向量 NumPy 数组。

5.  **`calculate_r_init(list_of_hsi_images: list[np.ndarray], H: int, W: int, B: int, roi_ratio: float) -> np.ndarray`**:
    *   输入 `list_of_hsi_images`，每个元素是 (H, W, B) 的高光谱图像。
    *   对于每个图像，在中心 `roi_ratio * H` x `roi_ratio * W`区域计算平均光谱向量 (B,)。
    *   将所有图像的平均光谱向量再进行平均，得到最终的 `S_bar_ROI` (B,)。
    *   创建一个 (H, W, B) 的 `R_init` 图像，其每个空间位置的光谱都等于 `S_bar_ROI`。
    *   返回 `R_init`。

**III. 数据加载器 (`data_loader.py`)**

```python
import os
import random
import torch
import numpy as np
from torch.utils.data import Dataset, DataLoader
import pandas as pd
# 假设 utils.py 和 config.py 在同一目录下或PYTHONPATH中
from . import utils
from . import config # 或者直接 import config

class AppleIntrinsicDataset(Dataset):
    def __init__(self, data_dir: str, angles: list[int], cfg):
        self.data_dir = data_dir
        self.angles = angles
        self.cfg = cfg # 保存config对象

        # 动态确定图像维度 (仅执行一次)
        if cfg.IMG_HEIGHT is None or cfg.IMG_WIDTH is None:
            first_csv_path = os.path.join(self.data_dir, f"hsi_point_fusion_{self.angles[0]}.csv")
            if not os.path.exists(first_csv_path):
                raise FileNotFoundError(f"Cannot find {first_csv_path} to determine image dimensions.")
            cfg.IMG_HEIGHT, cfg.IMG_WIDTH = utils.get_image_dims_from_csv(first_csv_path)
            print(f"Determined image dimensions: H={cfg.IMG_HEIGHT}, W={cfg.IMG_WIDTH}")

        self.H, self.W, self.B = cfg.IMG_HEIGHT, cfg.IMG_WIDTH, cfg.NUM_BANDS

        self.samples_data = [] # 存储每个视角的数据
        all_hsi_images_for_r_init = []
        all_point_clouds_flat = [] # 用于计算全局法向量

        for angle in self.angles:
            csv_path = os.path.join(self.data_dir, f"hsi_point_fusion_{angle}.csv")
            pixel_coords, pc_flat, hsi_flat = utils.load_raw_data_from_csv(csv_path)
            
            hsi_image = utils.create_map_from_points(pixel_coords, hsi_flat, self.H, self.W, self.B)
            all_hsi_images_for_r_init.append(hsi_image)
            all_point_clouds_flat.append(pc_flat) # 收集所有点云数据

            self.samples_data.append({
                'angle': angle,
                'hsi_image': hsi_image, # H, W, B
                'pixel_coords': pixel_coords, # N, 2
                'point_cloud_flat': pc_flat # N, 3
            })

        # 计算全局 R_init
        self.R_init = utils.calculate_r_init(all_hsi_images_for_r_init, self.H, self.W, self.B, cfg.R_INIT_ROI_RATIO) # H, W, B

        # 合并所有点云，去重，计算全局法向量，然后映射回每个视角
        # 注意：这里的点云来自于不同视角下的观测，它们可能对应苹果表面的同一物理点。
        # 一个简化的做法是：假设所有CSV中的点云(x,y,z)已经在同一坐标系下，可以合并它们。
        # 如果每个CSV的点云是独立的，则需要更复杂的配准。
        # 我们假设它们是同一苹果在稳定姿态下的多视角观测，可以简单合并。
        combined_pc_flat = np.concatenate(all_point_clouds_flat, axis=0)
        # 可选：对 combined_pc_flat 进行体素下采样去重以减少计算量
        # pcd_combined = o3d.geometry.PointCloud()
        # pcd_combined.points = o3d.utility.Vector3dVector(combined_pc_flat)
        # pcd_combined_downsampled = pcd_combined.voxel_down_sample(voxel_size=0.001) # 示例voxel_size
        # combined_pc_flat_unique = np.asarray(pcd_combined_downsampled.points)
        # normals_flat_unique = utils.calculate_normals_open3d(combined_pc_flat_unique, cfg.NORMAL_EST_RADIUS, cfg.NORMAL_EST_MAX_NN)
        # 现在的问题是如何将这些基于 unique 点的法向量映射回原始的、每个视角的 pixel_coords。
        #
        # 简化策略1：为每个视角的点云单独计算法向量，然后构建 N_map。这可能导致不同视角N_map不完全一致。
        # 简化策略2（当前采用）：假设苹果是刚体，其表面法向量是固定的。
        # 我们需要一个 " canonical " 点云和其法向量。
        # 这里我们用第一个视角的点云和像素坐标来构建 N_map 和 C_map 作为代表。
        # 这是一个强假设，理想情况下应该有苹果的完整3D模型。
        
        representative_sample_idx = 0 # 使用第一个角度的数据作为代表来构建共享的N_map, C_map
        rep_pixel_coords = self.samples_data[representative_sample_idx]['pixel_coords']
        rep_pc_flat = self.samples_data[representative_sample_idx]['point_cloud_flat']

        rep_normals_flat = utils.calculate_normals_open3d(rep_pc_flat, cfg.NORMAL_EST_RADIUS, cfg.NORMAL_EST_MAX_NN) # N, 3
        
        self.N_map_shared = utils.create_map_from_points(rep_pixel_coords, rep_normals_flat, self.H, self.W, cfg.NUM_NORMAL_COORDS, default_val=0.0) # H, W, 3
        self.C_map_shared = utils.create_map_from_points(rep_pixel_coords, rep_pc_flat, self.H, self.W, cfg.NUM_POINT_CLOUD_COORDS, default_val=0.0) # H, W, 3


    def __len__(self):
        # 每次迭代处理一对视角，所以长度是视角数（可以形成 N*(N-1)/2 对，但为了简单，就按视角数，内部随机配对）
        return len(self.samples_data)

    def __getitem__(self, idx):
        # 获取视角1的数据
        sample1_data = self.samples_data[idx]
        I1 = sample1_data['hsi_image'].astype(np.float32) # H,W,B

        # 随机选择视角2的数据 (确保与视角1不同)
        idx2 = idx
        while idx2 == idx: # 确保不是同一个视角
            idx2 = random.randint(0, len(self.samples_data) - 1)
        sample2_data = self.samples_data[idx2]
        I2 = sample2_data['hsi_image'].astype(np.float32) # H,W,B

        # 共享的几何和物理先验 (H,W,C)
        N_map = self.N_map_shared.astype(np.float32)
        C_map = self.C_map_shared.astype(np.float32)
        R_init = self.R_init.astype(np.float32)

        # 转换为 PyTorch 张量并 permute (C,H,W)
        I1_tensor = torch.from_numpy(I1).permute(2, 0, 1)
        I2_tensor = torch.from_numpy(I2).permute(2, 0, 1)
        N_map_tensor = torch.from_numpy(N_map).permute(2, 0, 1)
        C_map_tensor = torch.from_numpy(C_map).permute(2, 0, 1)
        R_init_tensor = torch.from_numpy(R_init).permute(2, 0, 1)

        # 构建网络输入 X = concat(I, N_map, C_map, R_init)
        X1_tensor = torch.cat([I1_tensor, N_map_tensor, C_map_tensor, R_init_tensor], dim=0)
        X2_tensor = torch.cat([I2_tensor, N_map_tensor, C_map_tensor, R_init_tensor], dim=0)

        # 对数变换
        log_X1 = torch.log(X1_tensor + self.cfg.LOG_EPSILON)
        log_I1_target = torch.log(I1_tensor + self.cfg.LOG_EPSILON)
        log_X2 = torch.log(X2_tensor + self.cfg.LOG_EPSILON)
        log_I2_target = torch.log(I2_tensor + self.cfg.LOG_EPSILON)
        
        return log_X1, log_I1_target, log_X2, log_I2_target

def get_dataloader(cfg):
    # 在创建dataset前确保config中的IMG_HEIGHT, IMG_WIDTH已通过get_image_dims_from_csv设置
    # 这个逻辑可以放在主脚本或train.py中
    dataset = AppleIntrinsicDataset(data_dir=cfg.DATA_DIR, angles=cfg.ANGLES, cfg=cfg)
    dataloader = DataLoader(dataset, batch_size=cfg.BATCH_SIZE, shuffle=True, num_workers=0, pin_memory=True) # num_workers视情况调整
    return dataloader

```

**IV. 网络模型 (`model.py`)**

```python
import torch
import torch.nn as nn
from . import config # 假设config.py在同一目录

# 一个简单的U-Net风格的构建块
class ConvBlock(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size=3, padding=1, use_batchnorm=True):
        super().__init__()
        layers = [nn.Conv2d(in_channels, out_channels, kernel_size=kernel_size, padding=padding)]
        if use_batchnorm:
            layers.append(nn.BatchNorm2d(out_channels))
        layers.append(nn.ReLU(inplace=True))
        self.block = nn.Sequential(*layers)

    def forward(self, x):
        return self.block(x)

class UNetEncoderBlock(nn.Module):
    def __init__(self, in_channels, out_channels, use_batchnorm=True):
        super().__init__()
        self.conv_block = nn.Sequential(
            ConvBlock(in_channels, out_channels, use_batchnorm=use_batchnorm),
            ConvBlock(out_channels, out_channels, use_batchnorm=use_batchnorm)
        )
        self.pool = nn.MaxPool2d(2, 2)

    def forward(self, x):
        skip = self.conv_block(x)
        out = self.pool(skip)
        return out, skip

class UNetDecoderBlock(nn.Module):
    def __init__(self, in_channels, skip_channels, out_channels, use_batchnorm=True):
        super().__init__()
        self.upconv = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)
        self.conv_block = nn.Sequential(
            ConvBlock(in_channels // 2 + skip_channels, out_channels, use_batchnorm=use_batchnorm),
            ConvBlock(out_channels, out_channels, use_batchnorm=use_batchnorm)
        )

    def forward(self, x, skip_connection):
        x = self.upconv(x)
        # 处理可能的尺寸不匹配 (由于池化和转置卷积)
        if x.shape != skip_connection.shape:
            # print(f"Shape mismatch: x={x.shape}, skip={skip_connection.shape}") # Debug
            diffY = skip_connection.size()[2] - x.size()[2]
            diffX = skip_connection.size()[3] - x.size()[3]
            x = nn.functional.pad(x, [diffX // 2, diffX - diffX // 2,
                                      diffY // 2, diffY - diffY // 2])
        x = torch.cat([x, skip_connection], dim=1)
        return self.conv_block(x)

class ReflectanceEstimationSubnetwork(nn.Module): # RES
    def __init__(self, input_channels, output_bands, base_channels=64):
        super().__init__()
        # Encoder
        self.enc1 = UNetEncoderBlock(input_channels, base_channels) # 64
        self.enc2 = UNetEncoderBlock(base_channels, base_channels*2) # 128
        self.enc3 = UNetEncoderBlock(base_channels*2, base_channels*4) # 256
        
        # Bottleneck
        self.bottleneck = nn.Sequential(
            ConvBlock(base_channels*4, base_channels*8), # 512
            ConvBlock(base_channels*8, base_channels*4) # 256 , to match enc3 output channels for skip
        )

        # Decoder
        self.dec1 = UNetDecoderBlock(base_channels*4, base_channels*4, base_channels*2) # input from bottleneck, skip from enc3
        self.dec2 = UNetDecoderBlock(base_channels*2, base_channels*2, base_channels) # input from dec1, skip from enc2
        self.dec3 = UNetDecoderBlock(base_channels, base_channels, base_channels) # input from dec2, skip from enc1
        
        self.final_conv = nn.Conv2d(base_channels, output_bands, kernel_size=1)

    def forward(self, x):
        x1, skip1 = self.enc1(x)
        x2, skip2 = self.enc2(x1)
        x3, skip3 = self.enc3(x2)
        
        bottle_out = self.bottleneck(x3)
        
        d1 = self.dec1(bottle_out, skip3)
        d2 = self.dec2(d1, skip2)
        d3 = self.dec3(d2, skip1)
        
        return self.final_conv(d3)


class ShadingEstimationSubnetwork(nn.Module): # SES
    # 可以与RES使用相同的U-Net结构，或者根据需要调整
    def __init__(self, input_channels, output_bands, base_channels=64):
        super().__init__()
        # 与RES相同的结构，可以复用或重新定义以允许不同参数
        self.net = ReflectanceEstimationSubnetwork(input_channels, output_bands, base_channels)

    def forward(self, x):
        return self.net(x)


class IntrinsicDecompositionNet(nn.Module):
    def __init__(self, cfg):
        super().__init__()
        # 输入通道数 = I_bands + N_map_channels + C_map_channels + R_init_bands
        input_ch = cfg.NUM_BANDS + cfg.NUM_NORMAL_COORDS + cfg.NUM_POINT_CLOUD_COORDS + cfg.NUM_BANDS
        
        # 输出通道数对于 R 和 S 都是原始高光谱波段数
        output_ch_R = cfg.NUM_BANDS
        output_ch_S = cfg.NUM_BANDS # 假设阴影也是高光谱的

        self.res_net = ReflectanceEstimationSubnetwork(input_ch, output_ch_R)
        self.ses_net = ShadingEstimationSubnetwork(input_ch, output_ch_S)

    def forward(self, log_X):
        log_R_pred = self.res_net(log_X)
        log_S_pred = self.ses_net(log_X)
        return log_R_pred, log_S_pred
```

**V. 损失函数 (`losses.py`)**

```python
import torch
import torch.nn.functional as F
from . import config # 假设config.py在同一目录

def reconstruction_loss(log_R_pred, log_S_pred, log_I_target):
    return F.l1_loss(log_R_pred + log_S_pred, log_I_target)

def reflectance_smoothness_loss_2d(log_R_pred):
    # 计算水平和垂直梯度
    grad_r_x = torch.abs(log_R_pred[:, :, :, :-1] - log_R_pred[:, :, :, 1:])
    grad_r_y = torch.abs(log_R_pred[:, :, :-1, :] - log_R_pred[:, :, 1:, :])
    
    loss = torch.mean(grad_r_x) + torch.mean(grad_r_y)
    return loss / 2.0 # 平均两个方向的梯度

def shading_normal_consistency_loss(log_S_pred, N_map_input, alpha):
    # log_S_pred: (B, NumBands, H, W)
    # N_map_input: (B, 3, H, W)
    
    # 1. 将log_S转换为单通道标量阴影图
    log_S_scalar = torch.mean(log_S_pred, dim=1, keepdim=True) # (B, 1, H, W)

    # 2. 计算标量阴影图的空间梯度
    # PyTorch 1.8+ has torch.gradient
    if hasattr(torch, 'gradient'):
        grad_S_y, grad_S_x = torch.gradient(log_S_scalar, dim=(-2, -1))
    else: # 简易手动梯度
        grad_S_x = log_S_scalar[:,:,:,:-1] - log_S_scalar[:,:,:,1:]
        grad_S_y = log_S_scalar[:,:,:-1,:] - log_S_scalar[:,:,1:,:]
        # 需要pad回原大小以匹配N_map梯度，或对N_map梯度做同样裁剪
        grad_S_x = F.pad(grad_S_x, (0,1,0,0), mode='replicate')
        grad_S_y = F.pad(grad_S_y, (0,0,0,1), mode='replicate')
        
    # 3. 计算法向量图N_map的空间梯度代理
    #    这里我们计算N_map每个通道梯度的绝对值之和的平均作为法线变化的一个代理
    grad_N_proxy_channels = []
    for i in range(N_map_input.shape[1]): # 遍历Nx, Ny, Nz
        if hasattr(torch, 'gradient'):
            grad_ch_y, grad_ch_x = torch.gradient(N_map_input[:, i:i+1, :, :], dim=(-2,-1))
        else:
            grad_ch_x = N_map_input[:, i:i+1, :, :-1] - N_map_input[:, i:i+1, :, 1:]
            grad_ch_y = N_map_input[:, i:i+1, :-1, :] - N_map_input[:, i:i+1, 1:, :]
            grad_ch_x = F.pad(grad_ch_x, (0,1,0,0), mode='replicate')
            grad_ch_y = F.pad(grad_ch_y, (0,0,0,1), mode='replicate')
        grad_N_proxy_channels.append(torch.abs(grad_ch_x) + torch.abs(grad_ch_y))
    
    grad_N_proxy = torch.mean(torch.stack(grad_N_proxy_channels, dim=0), dim=0) # (B,1,H,W)

    # 4. 计算损失
    # 鼓励当法向量变化小 (grad_N_proxy小) 时，阴影的梯度也小
    loss = torch.mean(torch.abs(grad_S_x) * torch.exp(-alpha * grad_N_proxy) + \
                      torch.abs(grad_S_y) * torch.exp(-alpha * grad_N_proxy))
    return loss / 2.0

def reflectance_consistency_loss(log_R_1_pred, log_R_2_pred):
    return F.l1_loss(log_R_1_pred, log_R_2_pred)
```

**VI. 训练脚本 (`train.py`)**

```python
import torch
import torch.optim as optim
import os
# 假设以下模块在models_code目录下
from . import config as cfg # 使用 as cfg 以明确
from .data_loader import get_dataloader # 如果 get_dataloader 定义在 data_loader.py
from .model import IntrinsicDecompositionNet
from .losses import (
    reconstruction_loss,
    reflectance_smoothness_loss_2d,
    shading_normal_consistency_loss,
    reflectance_consistency_loss
)
from .utils import get_image_dims_from_csv # 导入get_image_dims_from_csv

def train_model():
    # 动态确定图像维度并更新config (如果尚未设置)
    # 这应该在创建Dataset/DataLoader之前完成
    if cfg.IMG_HEIGHT is None or cfg.IMG_WIDTH is None:
        if not cfg.ANGLES:
            raise ValueError("Config: ANGLES list is empty, cannot determine image dimensions.")
        first_csv_path = os.path.join(cfg.DATA_DIR, f"hsi_point_fusion_{cfg.ANGLES[0]}.csv")
        if not os.path.exists(first_csv_path):
            raise FileNotFoundError(f"Cannot find {first_csv_path} to determine image dimensions.")
        cfg.IMG_HEIGHT, cfg.IMG_WIDTH = get_image_dims_from_csv(first_csv_path)
        print(f"Updated config with image dimensions: H={cfg.IMG_HEIGHT}, W={cfg.IMG_WIDTH}")
    
    device = torch.device(cfg.DEVICE if torch.cuda.is_available() and cfg.DEVICE=="cuda" else "cpu")
    print(f"Using device: {device}")

    # 数据加载
    dataloader = get_dataloader(cfg)

    # 模型初始化
    model = IntrinsicDecompositionNet(cfg).to(device)
    optimizer = optim.Adam(model.parameters(), lr=cfg.LEARNING_RATE)

    print("Starting training...")
    for epoch in range(cfg.NUM_EPOCHS):
        model.train()
        epoch_loss = 0.0
        
        for batch_idx, (log_X1, log_I1_target, log_X2, log_I2_target) in enumerate(dataloader):
            log_X1, log_I1_target = log_X1.to(device), log_I1_target.to(device)
            log_X2, log_I2_target = log_X2.to(device), log_I2_target.to(device)

            optimizer.zero_grad()

            # 处理视角1
            log_R1_pred, log_S1_pred = model(log_X1)
            loss_rec1 = reconstruction_loss(log_R1_pred, log_S1_pred, log_I1_target)
            loss_rsmooth1 = reflectance_smoothness_loss_2d(log_R1_pred)
            # 提取N_map (假设它在I之后，C_map之前)
            # input_ch = cfg.NUM_BANDS (I) + cfg.NUM_NORMAL_COORDS (N) + cfg.NUM_POINT_CLOUD_COORDS (C) + cfg.NUM_BANDS (R_init)
            n_map_start_channel = cfg.NUM_BANDS
            n_map_end_channel = cfg.NUM_BANDS + cfg.NUM_NORMAL_COORDS
            N_map1_input = log_X1[:, n_map_start_channel:n_map_end_channel, :, :]
            loss_snorm1 = shading_normal_consistency_loss(log_S1_pred, N_map1_input, cfg.SHADING_NORMAL_ALPHA)
            
            # 处理视角2
            log_R2_pred, log_S2_pred = model(log_X2)
            loss_rec2 = reconstruction_loss(log_R2_pred, log_S2_pred, log_I2_target)
            loss_rsmooth2 = reflectance_smoothness_loss_2d(log_R2_pred)
            N_map2_input = log_X2[:, n_map_start_channel:n_map_end_channel, :, :] # N_map1和N_map2应相同
            loss_snorm2 = shading_normal_consistency_loss(log_S2_pred, N_map2_input, cfg.SHADING_NORMAL_ALPHA)

            # 一致性损失
            loss_rconst = reflectance_consistency_loss(log_R1_pred, log_R2_pred)

            # 总损失
            total_loss = (
                cfg.LAMBDA_REC * (loss_rec1 + loss_rec2) / 2.0 +
                cfg.LAMBDA_R_SMOOTH_2D * (loss_rsmooth1 + loss_rsmooth2) / 2.0 +
                cfg.LAMBDA_S_NORMAL * (loss_snorm1 + loss_snorm2) / 2.0 +
                cfg.LAMBDA_R_CONST * loss_rconst
            )

            total_loss.backward()
            optimizer.step()

            epoch_loss += total_loss.item()

            if batch_idx % 10 == 0: # 每10个batch打印一次日志
                print(f"Epoch [{epoch+1}/{cfg.NUM_EPOCHS}], Batch [{batch_idx+1}/{len(dataloader)}], Loss: {total_loss.item():.4f}")
        
        avg_epoch_loss = epoch_loss / len(dataloader)
        print(f"--- Epoch [{epoch+1}/{cfg.NUM_EPOCHS}] finished. Average Loss: {avg_epoch_loss:.4f} ---")

        # 可选：模型保存
        if (epoch + 1) % 10 == 0: # 每10个epoch保存一次
            model_save_path = os.path.join(cfg.SAVE_DIR if hasattr(cfg, 'SAVE_DIR') else ".", f"model_epoch_{epoch+1}.pth")
            torch.save(model.state_dict(), model_save_path)
            print(f"Model saved to {model_save_path}")

    print("Training finished.")

if __name__ == '__main__':
    # 创建一个SAVE_DIR用于保存模型 (如果config中没有定义)
    if not hasattr(cfg, 'SAVE_DIR'):
        cfg.SAVE_DIR = "./saved_models" # 定义保存模型的目录
    os.makedirs(cfg.SAVE_DIR, exist_ok=True)
    
    train_model()
```

**VII. (可选) Notebooks**
*   `data_exploration.ipynb`: 用于加载、可视化原始数据、中间特征图 (如N_map, C_map, R_init) 等。
*   `results_visualization.ipynb`: 用于加载训练好的模型，进行推理，并可视化分解结果 (I, R_pred, S_pred)。

---

**给Cursor的额外指令和注意事项：**

1.  **逐步生成：** 请先生成 `config.py`，然后是 `utils.py`，接着是 `data_loader.py`，然后是 `model.py`，再是 `losses.py`，最后是 `train.py`。
2.  **导入检查：** 请确保所有模块间的导入语句正确无误 (例如，使用相对导入 `from . import ...` 对于同一包内的模块)。
3.  **法向量方向：** 在 `utils.calculate_normals_open3d` 中，法向量方向的统一可能需要实验。如果初始实现复杂，可以先跳过显式的方向统一，但需注意这可能影响 `L_S_normal` 的效果。
4.  **`N_map_shared` 和 `C_map_shared` 的假设：** `AppleIntrinsicDataset` 中创建共享的 `N_map_shared` 和 `C_map_shared` 是基于第一个视角的点云数据。这是一个简化，理想情况下，应该有一个与所有视角都精确配准的完整苹果3D模型。如果实际效果不佳，这部分可能需要改进，例如，为每个视角单独计算和使用其自身的 `N_map` 和 `C_map`（但这样 `R_init` 也可能需要调整）。
5.  **调试信息：** 在开发过程中，可以在关键步骤加入 `print` 语句或使用调试器来检查张量的形状和值。
6.  **超参数调整：** 提供的损失权重和学习率是初始建议，实际训练中很可能需要调整。
7.  **`torch.gradient`：** 该函数在 PyTorch 1.8+ 中可用。如果使用的版本较低，`losses.py` 中的梯度计算需要使用手动差分并适当填充。已在代码中添加了兼容性处理的注释。
8.  **模型保存：** 在 `train.py` 中添加了简单的模型保存逻辑，请确保 `SAVE_DIR` 存在或被创建。

---

这个提示词应该足够全面，能够指导AI生成一个结构良好且功能完整的项目骨架。
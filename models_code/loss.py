# -*- coding: utf-8 -*-
"""
损失函数 - 包含重建损失、平滑性损失、法向量一致性损失和反射率一致性损失
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple


class ReconstructionLoss(nn.Module):
    """重建损失：L_rec = ||log(I) - (log(R) + log(S))||²"""
    
    def __init__(self):
        super(ReconstructionLoss, self).__init__()
        self.mse_loss = nn.MSELoss()
    
    def forward(self, log_input: torch.Tensor, log_reflectance: torch.Tensor, log_shading: torch.Tensor) -> torch.Tensor:
        """
        计算重建损失
        
        Args:
            log_input: 对数域输入图像 (B, C, H, W)
            log_reflectance: 对数域反射率 (B, C, H, W)
            log_shading: 对数域阴影 (B, C, H, W)
            
        Returns:
            loss: 重建损失值
        """
        log_reconstructed = log_reflectance + log_shading
        return self.mse_loss(log_reconstructed, log_input)


class ReflectanceSmoothness2DLoss(nn.Module):
    """反射率2D平滑性损失：L_R_smooth_2D = ||∇R||²"""

    def __init__(self, edge_threshold: float = 0.1):
        super(ReflectanceSmoothness2DLoss, self).__init__()
        self.edge_threshold = edge_threshold

    def forward(self, reflectance: torch.Tensor) -> torch.Tensor:
        """
        计算反射率2D平滑性损失，减少边界区域的影响

        Args:
            reflectance: 线性域反射率 (B, C, H, W)

        Returns:
            loss: 平滑性损失值
        """
        B, C, H, W = reflectance.shape

        # 计算水平和垂直梯度
        grad_x = torch.abs(reflectance[:, :, :, 1:] - reflectance[:, :, :, :-1])
        grad_y = torch.abs(reflectance[:, :, 1:, :] - reflectance[:, :, :-1, :])

        # 创建权重掩码，减少边界区域的权重
        # 边界区域权重较小，中心区域权重较大
        weight_x = torch.ones_like(grad_x)
        weight_y = torch.ones_like(grad_y)

        # 减少边界权重
        edge_width = max(1, min(H//10, W//10))  # 边界宽度为图像尺寸的10%

        # 水平梯度权重调整
        weight_x[:, :, :edge_width, :] *= 0.1  # 上边界
        weight_x[:, :, -edge_width:, :] *= 0.1  # 下边界
        weight_x[:, :, :, :edge_width] *= 0.1   # 左边界
        weight_x[:, :, :, -edge_width:] *= 0.1  # 右边界

        # 垂直梯度权重调整
        weight_y[:, :, :edge_width, :] *= 0.1  # 上边界
        weight_y[:, :, -edge_width:, :] *= 0.1  # 下边界
        weight_y[:, :, :, :edge_width] *= 0.1   # 左边界
        weight_y[:, :, :, -edge_width:] *= 0.1  # 右边界

        # 计算加权梯度损失
        loss_x = torch.mean(weight_x * grad_x ** 2)
        loss_y = torch.mean(weight_y * grad_y ** 2)

        return loss_x + loss_y


class ShadingNormalConsistencyLoss(nn.Module):
    """阴影-法向量一致性损失：L_S_normal = ||∇S - exp(-α||∇N||) * ∇N||²"""
    
    def __init__(self, alpha: float = 1.0):
        super(ShadingNormalConsistencyLoss, self).__init__()
        self.alpha = alpha
    
    def forward(self, shading: torch.Tensor, normal_map: torch.Tensor) -> torch.Tensor:
        """
        计算阴影-法向量一致性损失
        
        Args:
            shading: 线性域阴影 (B, C, H, W)
            normal_map: 法向量图 (B, 3, H, W)
            
        Returns:
            loss: 一致性损失值
        """
        # 计算阴影的梯度
        shading_grad_x = shading[:, :, :, 1:] - shading[:, :, :, :-1]  # (B, C, H, W-1)
        shading_grad_y = shading[:, :, 1:, :] - shading[:, :, :-1, :]  # (B, C, H-1, W)
        
        # 计算法向量的梯度
        normal_grad_x = normal_map[:, :, :, 1:] - normal_map[:, :, :, :-1]  # (B, 3, H, W-1)
        normal_grad_y = normal_map[:, :, 1:, :] - normal_map[:, :, :-1, :]  # (B, 3, H-1, W)
        
        # 计算法向量梯度的模长
        normal_grad_x_norm = torch.norm(normal_grad_x, dim=1, keepdim=True)  # (B, 1, H, W-1)
        normal_grad_y_norm = torch.norm(normal_grad_y, dim=1, keepdim=True)  # (B, 1, H-1, W)
        
        # 计算权重：exp(-α||∇N||)
        weight_x = torch.exp(-self.alpha * normal_grad_x_norm)  # (B, 1, H, W-1)
        weight_y = torch.exp(-self.alpha * normal_grad_y_norm)  # (B, 1, H-1, W)
        
        # 扩展法向量梯度到与阴影相同的通道数
        C = shading.shape[1]
        if C >= 3:
            # 如果阴影通道数大于等于3，重复法向量梯度
            repeat_times = C // 3
            if C % 3 != 0:
                repeat_times += 1
            normal_grad_x_expanded = normal_grad_x.repeat(1, repeat_times, 1, 1)[:, :C, :, :]
            normal_grad_y_expanded = normal_grad_y.repeat(1, repeat_times, 1, 1)[:, :C, :, :]
        else:
            # 如果阴影通道数小于3，截取法向量梯度
            normal_grad_x_expanded = normal_grad_x[:, :C, :, :]
            normal_grad_y_expanded = normal_grad_y[:, :C, :, :]
        
        # 计算期望的阴影梯度
        expected_shading_grad_x = weight_x * normal_grad_x_expanded
        expected_shading_grad_y = weight_y * normal_grad_y_expanded
        
        # 计算损失
        loss_x = torch.mean((shading_grad_x - expected_shading_grad_x) ** 2)
        loss_y = torch.mean((shading_grad_y - expected_shading_grad_y) ** 2)
        
        return loss_x + loss_y


class ReflectanceConsistencyLoss(nn.Module):
    """反射率一致性损失：L_R_const = ||R1 - R2||²"""

    def __init__(self):
        super(ReflectanceConsistencyLoss, self).__init__()
        self.mse_loss = nn.MSELoss()

    def forward(self, reflectance1: torch.Tensor, reflectance2: torch.Tensor) -> torch.Tensor:
        """
        计算两个视角间的反射率一致性损失

        Args:
            reflectance1: 第一个视角的反射率 (B, C, H, W)
            reflectance2: 第二个视角的反射率 (B, C, H, W)

        Returns:
            loss: 一致性损失值
        """
        return self.mse_loss(reflectance1, reflectance2)


class EdgePreservingLoss(nn.Module):
    """边界保持损失：保护图像边界区域的细节"""

    def __init__(self, edge_weight: float = 1.5):
        super(EdgePreservingLoss, self).__init__()
        self.edge_weight = edge_weight
        self.mse_loss = nn.MSELoss(reduction='none')

    def forward(self, predicted: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算边界保持损失

        Args:
            predicted: 预测的反射率 (B, C, H, W)
            target: 目标反射率 (B, C, H, W)

        Returns:
            loss: 边界保持损失值
        """
        B, C, H, W = predicted.shape

        # 计算基础MSE损失
        mse_loss = self.mse_loss(predicted, target)  # (B, C, H, W)

        # 创建边界权重掩码
        weight_mask = torch.ones_like(mse_loss)

        # 温和地增加边界区域的权重
        edge_width = max(1, min(H//15, W//15))  # 减小边界宽度

        # 边界区域权重温和增加
        weight_mask[:, :, :edge_width, :] *= self.edge_weight  # 上边界
        weight_mask[:, :, -edge_width:, :] *= self.edge_weight  # 下边界
        weight_mask[:, :, :, :edge_width] *= self.edge_weight   # 左边界
        weight_mask[:, :, :, -edge_width:] *= self.edge_weight  # 右边界

        # 应用权重并计算平均损失
        weighted_loss = weight_mask * mse_loss

        # 使用更温和的损失计算
        return torch.mean(weighted_loss)


class CombinedLoss(nn.Module):
    """组合损失函数"""
    
    def __init__(self, 
                 lambda_rec: float = 1.0,
                 lambda_r_smooth_2d: float = 0.1,
                 lambda_s_normal: float = 0.1,
                 lambda_r_const: float = 0.5,
                 shading_normal_alpha: float = 1.0):
        super(CombinedLoss, self).__init__()
        
        self.lambda_rec = lambda_rec
        self.lambda_r_smooth_2d = lambda_r_smooth_2d
        self.lambda_s_normal = lambda_s_normal
        self.lambda_r_const = lambda_r_const
        
        # 各个损失函数
        self.reconstruction_loss = ReconstructionLoss()
        self.reflectance_smoothness_loss = ReflectanceSmoothness2DLoss()
        self.shading_normal_loss = ShadingNormalConsistencyLoss(alpha=shading_normal_alpha)
        self.reflectance_consistency_loss = ReflectanceConsistencyLoss()
    
    def forward(self, 
                log_input1: torch.Tensor, log_input2: torch.Tensor,
                log_reflectance1: torch.Tensor, log_reflectance2: torch.Tensor,
                log_shading1: torch.Tensor, log_shading2: torch.Tensor,
                normal1: torch.Tensor, normal2: torch.Tensor) -> Tuple[torch.Tensor, Dict[str, float]]:
        """
        计算组合损失
        
        Args:
            log_input1, log_input2: 对数域输入图像
            log_reflectance1, log_reflectance2: 对数域反射率
            log_shading1, log_shading2: 对数域阴影
            normal1, normal2: 法向量图
            
        Returns:
            total_loss: 总损失
            loss_dict: 各项损失的字典
        """
        # 转换为线性域
        reflectance1 = torch.exp(log_reflectance1)
        reflectance2 = torch.exp(log_reflectance2)
        shading1 = torch.exp(log_shading1)
        shading2 = torch.exp(log_shading2)
        
        # 计算各项损失
        # 1. 重建损失
        rec_loss1 = self.reconstruction_loss(log_input1, log_reflectance1, log_shading1)
        rec_loss2 = self.reconstruction_loss(log_input2, log_reflectance2, log_shading2)
        rec_loss = (rec_loss1 + rec_loss2) / 2
        
        # 2. 反射率平滑性损失
        smooth_loss1 = self.reflectance_smoothness_loss(reflectance1)
        smooth_loss2 = self.reflectance_smoothness_loss(reflectance2)
        smooth_loss = (smooth_loss1 + smooth_loss2) / 2
        
        # 3. 阴影-法向量一致性损失
        normal_loss1 = self.shading_normal_loss(shading1, normal1)
        normal_loss2 = self.shading_normal_loss(shading2, normal2)
        normal_loss = (normal_loss1 + normal_loss2) / 2
        
        # 4. 反射率一致性损失
        consistency_loss = self.reflectance_consistency_loss(reflectance1, reflectance2)
        
        # 组合总损失
        total_loss = (self.lambda_rec * rec_loss + 
                     self.lambda_r_smooth_2d * smooth_loss +
                     self.lambda_s_normal * normal_loss +
                     self.lambda_r_const * consistency_loss)
        
        # 损失字典（用于记录）
        loss_dict = {
            'total_loss': total_loss.item(),
            'reconstruction_loss': rec_loss.item(),
            'smoothness_loss': smooth_loss.item(),
            'normal_consistency_loss': normal_loss.item(),
            'reflectance_consistency_loss': consistency_loss.item()
        }
        
        return total_loss, loss_dict
    
    def update_weights(self, 
                      lambda_rec: float = None,
                      lambda_r_smooth_2d: float = None,
                      lambda_s_normal: float = None,
                      lambda_r_const: float = None):
        """更新损失权重"""
        if lambda_rec is not None:
            self.lambda_rec = lambda_rec
        if lambda_r_smooth_2d is not None:
            self.lambda_r_smooth_2d = lambda_r_smooth_2d
        if lambda_s_normal is not None:
            self.lambda_s_normal = lambda_s_normal
        if lambda_r_const is not None:
            self.lambda_r_const = lambda_r_const
        
        print(f"损失权重更新: rec={self.lambda_rec}, smooth={self.lambda_r_smooth_2d}, "
              f"normal={self.lambda_s_normal}, const={self.lambda_r_const}")


def create_loss_function(lambda_rec: float = 1.0,
                        lambda_r_smooth_2d: float = 0.1,
                        lambda_s_normal: float = 0.1,
                        lambda_r_const: float = 0.5,
                        shading_normal_alpha: float = 1.0) -> CombinedLoss:
    """
    创建损失函数
    
    Args:
        lambda_rec: 重建损失权重
        lambda_r_smooth_2d: 平滑性损失权重
        lambda_s_normal: 法向量一致性损失权重
        lambda_r_const: 反射率一致性损失权重
        shading_normal_alpha: 阴影-法向量损失的alpha参数
        
    Returns:
        loss_function: 组合损失函数
    """
    loss_function = CombinedLoss(
        lambda_rec=lambda_rec,
        lambda_r_smooth_2d=lambda_r_smooth_2d,
        lambda_s_normal=lambda_s_normal,
        lambda_r_const=lambda_r_const,
        shading_normal_alpha=shading_normal_alpha
    )
    
    print(f"损失函数创建完成:")
    print(f"  重建损失权重: {lambda_rec}")
    print(f"  平滑性损失权重: {lambda_r_smooth_2d}")
    print(f"  法向量一致性损失权重: {lambda_s_normal}")
    print(f"  反射率一致性损失权重: {lambda_r_const}")
    print(f"  阴影-法向量alpha: {shading_normal_alpha}")
    
    return loss_function


def test_loss_functions():
    """测试损失函数"""
    print("测试损失函数...")
    
    # 模拟数据
    batch_size = 2
    channels = 224
    height, width = 32, 32
    
    # 创建随机张量
    log_input1 = torch.randn(batch_size, channels, height, width)
    log_input2 = torch.randn(batch_size, channels, height, width)
    log_reflectance1 = torch.randn(batch_size, channels, height, width)
    log_reflectance2 = torch.randn(batch_size, channels, height, width)
    log_shading1 = torch.randn(batch_size, channels, height, width)
    log_shading2 = torch.randn(batch_size, channels, height, width)
    normal1 = torch.randn(batch_size, 3, height, width)
    normal2 = torch.randn(batch_size, 3, height, width)
    
    # 创建损失函数
    loss_fn = create_loss_function()
    
    # 计算损失
    total_loss, loss_dict = loss_fn(
        log_input1, log_input2,
        log_reflectance1, log_reflectance2,
        log_shading1, log_shading2,
        normal1, normal2
    )
    
    print(f"损失计算结果:")
    for key, value in loss_dict.items():
        print(f"  {key}: {value:.6f}")
    
    print("✓ 损失函数测试通过")


if __name__ == "__main__":
    test_loss_functions()

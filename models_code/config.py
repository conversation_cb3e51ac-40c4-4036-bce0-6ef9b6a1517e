# -*- coding: utf-8 -*-
"""
配置文件 - 基于多模态数据与物理先验的苹果本征高光谱特性提取模型
"""

import os

# ==================== 数据路径配置 ====================
# 数据根目录
DATA_DIR = "/Users/<USER>/Desktop/研究生学业/高光谱原理解析/528/fusion_data"

# 可用角度列表
ANGLES = [35, 40, 45, 50, 55, 60, 65, 70]

# 参考角度（用于计算R_init）
REFERENCE_ANGLE = 45

# ==================== 数据维度配置 ====================
# 图像维度（将在首次加载数据时动态确定）
IMG_HEIGHT = None
IMG_WIDTH = None

# 高光谱波段数
NUM_BANDS = 224

# 点云坐标维度
NUM_POINT_CLOUD_COORDS = 3  # x, y, z

# 法向量维度
NUM_NORMAL_COORDS = 3  # normal_x, normal_y, normal_z

# ==================== 训练超参数 ====================
# 批次大小（每个样本包含一对视角）
BATCH_SIZE = 2

# 学习率
LEARNING_RATE = 1e-4

# 训练轮数
NUM_EPOCHS = 6

# 设备选择
DEVICE = "mps"  # 可选: "cuda", "mps", "cpu"

# ==================== 损失函数权重 ====================
# 重建损失权重
LAMBDA_REC = 1.0

# 反射率2D平滑性损失权重
LAMBDA_R_SMOOTH_2D = 0.01

# 阴影-法向量一致性损失权重
LAMBDA_S_NORMAL = 0.01

# 反射率一致性损失权重
LAMBDA_R_CONST = 0.1

# ==================== 数据处理参数 ====================
# 对数变换的小值防止log(0)
LOG_EPSILON = 1e-6

# R_init计算时中心区域的比例
R_INIT_ROI_RATIO = 0.2

# ==================== 法向量估计参数 ====================
# Open3D法向量估计半径（假设点云单位是mm）
NORMAL_EST_RADIUS = 5.0  # 5mm

# 法向量估计最大邻居数
NORMAL_EST_MAX_NN = 30

# ==================== 阴影-法向量损失参数 ====================
# L_S_normal中exp(-alpha * grad_N_proxy)的alpha参数
SHADING_NORMAL_ALPHA = 1.0

# ==================== 模型架构参数 ====================
# U-Net基础通道数
BASE_CHANNELS = 64

# 是否使用批归一化
USE_BATCH_NORM = True

# ==================== 训练配置 ====================
# 模型保存目录
SAVE_DIR = "./saved_models"

# 模型保存频率（每多少个epoch保存一次）
SAVE_FREQUENCY = 10

# 检查点保存频率（每多少个epoch保存检查点）
CHECKPOINT_FREQUENCY = 20  # 减少保存频率以提高速度

# 日志打印频率（每多少个batch打印一次）
LOG_FREQUENCY = 10

# ==================== 验证和测试配置 ====================
# 验证集比例
VAL_RATIO = 0.2

# 是否在训练过程中进行验证
VALIDATE_DURING_TRAINING = True

# 验证频率（每多少个epoch验证一次）
VAL_FREQUENCY = 5

# ==================== 数据增强配置 ====================
# 是否使用数据增强
USE_DATA_AUGMENTATION = False

# 随机旋转角度范围
ROTATION_RANGE = 5.0

# 随机缩放范围
SCALE_RANGE = 0.1

# ==================== 高级配置 ====================
# 是否使用混合精度训练
USE_MIXED_PRECISION = False

# 梯度裁剪阈值
GRAD_CLIP_THRESHOLD = 1.0

# 早停耐心值
EARLY_STOPPING_PATIENCE = 20

# 学习率调度器类型
LR_SCHEDULER = "cosine"  # 可选: "cosine", "step", "plateau"

# ==================== 可视化配置 ====================
# 是否保存训练过程中的可视化结果
SAVE_VISUALIZATIONS = True

# 可视化保存目录
VIS_DIR = "./visualizations"

# 可视化频率
VIS_FREQUENCY = 20

# ==================== 辅助函数 ====================
def create_directories():
    """创建必要的目录"""
    directories = [SAVE_DIR, VIS_DIR]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"创建目录: {directory}")

def get_device():
    """获取可用的计算设备"""
    import torch
    
    if DEVICE == "cuda" and torch.cuda.is_available():
        device = torch.device("cuda")
        print(f"使用GPU: {torch.cuda.get_device_name()}")
    elif DEVICE == "mps" and torch.backends.mps.is_available():
        device = torch.device("mps")
        print("使用Apple Silicon GPU")
    else:
        device = torch.device("cpu")
        print("使用CPU")
    
    return device

def print_config():
    """打印配置信息"""
    print("=" * 50)
    print("模型配置信息")
    print("=" * 50)
    print(f"数据目录: {DATA_DIR}")
    print(f"可用角度: {ANGLES}")
    print(f"参考角度: {REFERENCE_ANGLE}")
    print(f"图像尺寸: {IMG_HEIGHT} × {IMG_WIDTH}")
    print(f"高光谱波段数: {NUM_BANDS}")
    print(f"批次大小: {BATCH_SIZE}")
    print(f"学习率: {LEARNING_RATE}")
    print(f"训练轮数: {NUM_EPOCHS}")
    print(f"计算设备: {DEVICE}")
    print("损失函数权重:")
    print(f"  重建损失: {LAMBDA_REC}")
    print(f"  平滑性损失: {LAMBDA_R_SMOOTH_2D}")
    print(f"  法向量一致性损失: {LAMBDA_S_NORMAL}")
    print(f"  反射率一致性损失: {LAMBDA_R_CONST}")
    print("=" * 50)

# 验证配置的合理性
def validate_config():
    """验证配置参数的合理性"""
    assert REFERENCE_ANGLE in ANGLES, f"参考角度{REFERENCE_ANGLE}不在可用角度列表中"
    assert 0 < R_INIT_ROI_RATIO < 1, "R_INIT_ROI_RATIO应该在(0,1)范围内"
    assert BATCH_SIZE > 0, "批次大小应该大于0"
    assert LEARNING_RATE > 0, "学习率应该大于0"
    assert NUM_EPOCHS > 0, "训练轮数应该大于0"
    
    # 检查数据目录是否存在
    if not os.path.exists(DATA_DIR):
        print(f"警告: 数据目录不存在: {DATA_DIR}")
    
    print("✓ 配置验证通过")

if __name__ == "__main__":
    validate_config()
    create_directories()
    print_config()

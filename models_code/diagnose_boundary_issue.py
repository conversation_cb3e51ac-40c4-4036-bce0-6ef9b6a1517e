#!/usr/bin/env python3
"""
边界问题诊断脚本
分析反射率分解中的边界效应问题
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import torch
from matplotlib.gridspec import GridSpec

from config import *
from dataset import MultiViewHSIDataset
from model import HyperspectralDecompositionNet
from utils import load_raw_data_from_csv, create_map_from_points_legacy


def analyze_data_distribution(data_dir: str, angles: list):
    """分析数据分布，特别是边界区域的数据密度"""
    print("=== 数据分布分析 ===")
    
    fig, axes = plt.subplots(2, len(angles), figsize=(4*len(angles), 8))
    if len(angles) == 1:
        axes = axes.reshape(-1, 1)
    
    for i, angle in enumerate(angles):
        csv_path = os.path.join(data_dir, f"hsi_point_fusion_{angle}_with_normals_oriented.csv")
        
        if not os.path.exists(csv_path):
            print(f"警告: 文件不存在 {csv_path}")
            continue
        
        # 加载数据
        pixel_coords, _, _, hsi_data = load_raw_data_from_csv(csv_path)
        
        # 获取图像尺寸
        H = int(np.max(pixel_coords[:, 0])) + 1
        W = int(np.max(pixel_coords[:, 1])) + 1
        
        # 创建密度图
        density_map = np.zeros((H, W))
        for coord in pixel_coords:
            if 0 <= coord[0] < H and 0 <= coord[1] < W:
                density_map[int(coord[0]), int(coord[1])] = 1
        
        # 创建HSI图像
        hsi_map = create_map_from_points_legacy(pixel_coords, hsi_data, H, W, hsi_data.shape[1])
        
        # 显示密度图
        axes[0, i].imshow(density_map, cmap='viridis')
        axes[0, i].set_title(f'数据密度 - {angle}°')
        axes[0, i].axis('off')
        
        # 显示HSI第一个波段
        axes[1, i].imshow(hsi_map[:, :, 0], cmap='viridis')
        axes[1, i].set_title(f'HSI Band 1 - {angle}°')
        axes[1, i].axis('off')
        
        # 分析边界数据密度
        edge_width = max(1, min(H//10, W//10))
        
        # 计算各区域的数据密度
        total_pixels = H * W
        edge_pixels = 2 * edge_width * (H + W) - 4 * edge_width * edge_width
        center_pixels = total_pixels - edge_pixels
        
        edge_data_count = np.sum(density_map[:edge_width, :]) + \
                         np.sum(density_map[-edge_width:, :]) + \
                         np.sum(density_map[:, :edge_width]) + \
                         np.sum(density_map[:, -edge_width:])
        
        center_data_count = np.sum(density_map[edge_width:-edge_width, edge_width:-edge_width])
        
        edge_density = edge_data_count / edge_pixels if edge_pixels > 0 else 0
        center_density = center_data_count / center_pixels if center_pixels > 0 else 0
        
        print(f"角度 {angle}°:")
        print(f"  图像尺寸: {H} × {W}")
        print(f"  总数据点: {len(pixel_coords)}")
        print(f"  边界密度: {edge_density:.3f}")
        print(f"  中心密度: {center_density:.3f}")
        print(f"  密度比值: {center_density/edge_density:.3f}" if edge_density > 0 else "  密度比值: inf")
    
    plt.tight_layout()
    plt.savefig('data_distribution_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()


def analyze_model_predictions(model_path: str, data_dir: str, angles: list):
    """分析模型预测结果，特别是边界区域"""
    print("\n=== 模型预测分析 ===")
    
    # 加载模型
    if not os.path.exists(model_path):
        print(f"模型文件不存在: {model_path}")
        return
    
    # 创建数据集
    dataset = MultiViewHSIDataset(data_dir, angles)
    
    # 创建模型
    input_channels = dataset.get_input_channels()
    output_channels = dataset.get_output_channels()
    model = HyperspectralDecompositionNet(input_channels, output_channels)
    
    # 加载权重
    checkpoint = torch.load(model_path, map_location='cpu')
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # 获取一个样本
    sample = dataset[0]
    
    with torch.no_grad():
        # 预测
        input1 = sample['input1'].unsqueeze(0)  # 添加batch维度
        input2 = sample['input2'].unsqueeze(0)
        
        log_reflectance1, log_shading1 = model(input1)
        log_reflectance2, log_shading2 = model(input2)
        
        # 转换为线性域
        reflectance1 = torch.exp(log_reflectance1).squeeze(0).permute(1, 2, 0).numpy()
        reflectance2 = torch.exp(log_reflectance2).squeeze(0).permute(1, 2, 0).numpy()
        
        # 原始HSI
        hsi1 = sample['hsi1'].permute(1, 2, 0).numpy()
        hsi2 = sample['hsi2'].permute(1, 2, 0).numpy()
    
    # 分析边界效应
    H, W, C = reflectance1.shape
    edge_width = max(1, min(H//10, W//10))
    
    # 提取不同区域
    # 边界区域
    edge_mask = np.zeros((H, W), dtype=bool)
    edge_mask[:edge_width, :] = True  # 上边界
    edge_mask[-edge_width:, :] = True  # 下边界
    edge_mask[:, :edge_width] = True   # 左边界
    edge_mask[:, -edge_width:] = True  # 右边界
    
    # 中心区域
    center_mask = np.zeros((H, W), dtype=bool)
    center_mask[edge_width:-edge_width, edge_width:-edge_width] = True
    
    # 计算统计信息
    print(f"反射率统计分析:")
    print(f"  图像尺寸: {H} × {W}")
    print(f"  边界宽度: {edge_width}")
    
    for name, data in [("原始HSI1", hsi1), ("预测反射率1", reflectance1), 
                       ("原始HSI2", hsi2), ("预测反射率2", reflectance2)]:
        edge_mean = np.mean(data[edge_mask])
        center_mean = np.mean(data[center_mask])
        edge_std = np.std(data[edge_mask])
        center_std = np.std(data[center_mask])
        
        print(f"  {name}:")
        print(f"    边界区域: 均值={edge_mean:.4f}, 标准差={edge_std:.4f}")
        print(f"    中心区域: 均值={center_mean:.4f}, 标准差={center_std:.4f}")
        print(f"    均值比值: {center_mean/edge_mean:.3f}" if edge_mean != 0 else "    均值比值: inf")
    
    # 可视化对比
    fig = plt.figure(figsize=(16, 12))
    gs = GridSpec(3, 4, figure=fig, hspace=0.3, wspace=0.3)
    
    # 选择代表性波段
    band_idx = C // 2
    
    # 第一行：原始数据
    ax1 = fig.add_subplot(gs[0, 0])
    im1 = ax1.imshow(hsi1[:, :, band_idx], cmap='viridis')
    ax1.set_title(f'原始HSI1 - Band {band_idx}')
    ax1.axis('off')
    plt.colorbar(im1, ax=ax1, fraction=0.046, pad=0.04)
    
    ax2 = fig.add_subplot(gs[0, 1])
    im2 = ax2.imshow(hsi2[:, :, band_idx], cmap='viridis')
    ax2.set_title(f'原始HSI2 - Band {band_idx}')
    ax2.axis('off')
    plt.colorbar(im2, ax=ax2, fraction=0.046, pad=0.04)
    
    # 第二行：预测反射率
    ax3 = fig.add_subplot(gs[1, 0])
    im3 = ax3.imshow(reflectance1[:, :, band_idx], cmap='viridis')
    ax3.set_title(f'预测反射率1 - Band {band_idx}')
    ax3.axis('off')
    plt.colorbar(im3, ax=ax3, fraction=0.046, pad=0.04)
    
    ax4 = fig.add_subplot(gs[1, 1])
    im4 = ax4.imshow(reflectance2[:, :, band_idx], cmap='viridis')
    ax4.set_title(f'预测反射率2 - Band {band_idx}')
    ax4.axis('off')
    plt.colorbar(im4, ax=ax4, fraction=0.046, pad=0.04)
    
    # 第三行：差异图
    diff1 = np.abs(hsi1[:, :, band_idx] - reflectance1[:, :, band_idx])
    diff2 = np.abs(hsi2[:, :, band_idx] - reflectance2[:, :, band_idx])
    
    ax5 = fig.add_subplot(gs[2, 0])
    im5 = ax5.imshow(diff1, cmap='hot')
    ax5.set_title('|原始 - 预测| 差异1')
    ax5.axis('off')
    plt.colorbar(im5, ax=ax5, fraction=0.046, pad=0.04)
    
    ax6 = fig.add_subplot(gs[2, 1])
    im6 = ax6.imshow(diff2, cmap='hot')
    ax6.set_title('|原始 - 预测| 差异2')
    ax6.axis('off')
    plt.colorbar(im6, ax=ax6, fraction=0.046, pad=0.04)
    
    # 边界掩码可视化
    ax7 = fig.add_subplot(gs[0, 2])
    ax7.imshow(edge_mask.astype(float), cmap='gray')
    ax7.set_title('边界区域掩码')
    ax7.axis('off')
    
    ax8 = fig.add_subplot(gs[1, 2])
    ax8.imshow(center_mask.astype(float), cmap='gray')
    ax8.set_title('中心区域掩码')
    ax8.axis('off')
    
    plt.savefig('boundary_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()


def main():
    """主函数"""
    print("🔍 边界问题诊断工具")
    print("=" * 50)
    
    # 分析数据分布
    analyze_data_distribution(DATA_DIR, ANGLES)
    
    # 分析模型预测（如果模型存在）
    model_path = "checkpoints/best_model.pth"
    if os.path.exists(model_path):
        analyze_model_predictions(model_path, DATA_DIR, ANGLES)
    else:
        print(f"\n模型文件不存在: {model_path}")
        print("请先训练模型后再运行预测分析")
    
    print("\n✅ 诊断完成！")
    print("请查看生成的图像文件：")
    print("  - data_distribution_analysis.png: 数据分布分析")
    print("  - boundary_analysis.png: 边界效应分析")


if __name__ == "__main__":
    main()

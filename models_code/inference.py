# -*- coding: utf-8 -*-
"""
推理脚本 - 模型推理和结果可视化
"""

import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
import cv2

from config import *
from dataset import MultiViewHSIDataset
from model import create_model
from utils import visualize_spectrum, create_visualization_grid


class InferenceEngine:
    """推理引擎"""
    
    def __init__(self, model_path: str):
        """
        初始化推理引擎
        
        Args:
            model_path: 模型权重文件路径
        """
        self.device = get_device()
        
        # 创建数据集以获取配置信息
        self.dataset = MultiViewHSIDataset(DATA_DIR, ANGLES)
        
        # 获取输入输出通道数
        self.input_channels = self.dataset.get_input_channels()
        self.output_channels = self.dataset.get_output_channels()
        
        # 创建模型
        self.model = create_model(
            input_channels=self.input_channels,
            output_channels=self.output_channels,
            base_channels=BASE_CHANNELS,
            use_batch_norm=USE_BATCH_NORM,
            device=self.device
        )
        
        # 加载模型权重
        self.load_model(model_path)
        
        print(f"推理引擎初始化完成，模型加载自: {model_path}")
    
    def load_model(self, model_path: str):
        """加载模型权重"""
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        checkpoint = torch.load(model_path, map_location=self.device)
        
        if 'model_state_dict' in checkpoint:
            self.model.load_state_dict(checkpoint['model_state_dict'])
            print(f"从检查点加载模型，epoch: {checkpoint.get('epoch', 'unknown')}")
        else:
            self.model.load_state_dict(checkpoint)
            print("直接加载模型权重")
        
        self.model.eval()
    
    def infer_single_view(self, angle: int) -> Dict[str, np.ndarray]:
        """
        对单个视角进行推理
        
        Args:
            angle: 视角角度
            
        Returns:
            results: 包含推理结果的字典
        """
        if angle not in self.dataset.data:
            raise ValueError(f"角度 {angle} 的数据不存在")
        
        # 获取数据
        data = self.dataset.data[angle]
        
        # 构建输入特征
        input_features = self.dataset._build_input_features(data)
        
        # 转换为张量并添加batch维度
        input_tensor = torch.from_numpy(input_features).permute(2, 0, 1).unsqueeze(0).float()
        input_tensor = input_tensor.to(self.device)
        
        # 推理
        with torch.no_grad():
            log_reflectance, log_shading = self.model(input_tensor)
            
            # 转换为线性域
            reflectance, shading = self.model.get_linear_outputs(log_reflectance, log_shading)
            
            # 重建图像
            log_reconstructed = self.model.reconstruct_image(log_reflectance, log_shading)
            reconstructed = torch.exp(log_reconstructed)
        
        # 转换为numpy数组并移除batch维度
        results = {
            'original_hsi': data['hsi_map'],
            'reflectance': reflectance.squeeze(0).permute(1, 2, 0).cpu().numpy(),
            'shading': shading.squeeze(0).permute(1, 2, 0).cpu().numpy(),
            'reconstructed': reconstructed.squeeze(0).permute(1, 2, 0).cpu().numpy(),
            'normal_map': data['normal_map'],
            'coord_map': data['coord_map'],
            'log_reflectance': log_reflectance.squeeze(0).permute(1, 2, 0).cpu().numpy(),
            'log_shading': log_shading.squeeze(0).permute(1, 2, 0).cpu().numpy()
        }
        
        return results
    
    def infer_all_views(self) -> Dict[int, Dict[str, np.ndarray]]:
        """
        对所有视角进行推理
        
        Returns:
            all_results: 所有视角的推理结果
        """
        all_results = {}
        
        print("开始对所有视角进行推理...")
        for angle in self.dataset.angles:
            if angle in self.dataset.data:
                print(f"推理角度: {angle}°")
                results = self.infer_single_view(angle)
                all_results[angle] = results
        
        print("所有视角推理完成!")
        return all_results
    
    def visualize_results(self, results: Dict[str, np.ndarray], angle: int, save_dir: Optional[str] = None):
        """
        可视化单个视角的结果
        
        Args:
            results: 推理结果
            angle: 视角角度
            save_dir: 保存目录（可选）
        """
        # 创建保存目录
        if save_dir:
            os.makedirs(save_dir, exist_ok=True)
        
        # 1. 可视化RGB合成图像
        self._visualize_rgb_composite(results, angle, save_dir)
        
        # 2. 可视化光谱曲线
        self._visualize_spectral_curves(results, angle, save_dir)
        
        # 3. 可视化法向量和几何信息
        self._visualize_geometry(results, angle, save_dir)
        
        # 4. 可视化分解结果对比
        self._visualize_decomposition_comparison(results, angle, save_dir)
    
    def _visualize_rgb_composite(self, results: Dict[str, np.ndarray], angle: int, save_dir: Optional[str]):
        """可视化RGB合成图像"""
        # 选择RGB波段（假设红光~650nm, 绿光~550nm, 蓝光~450nm对应的波段索引）
        B = results['original_hsi'].shape[2]
        red_idx = int(B * (650-400)/(1000-400))
        green_idx = int(B * (550-400)/(1000-400))
        blue_idx = int(B * (450-400)/(1000-400))
        
        # 提取RGB通道并归一化
        def extract_rgb(hsi_data):
            rgb = np.stack([
                hsi_data[:, :, red_idx],
                hsi_data[:, :, green_idx],
                hsi_data[:, :, blue_idx]
            ], axis=2)
            # 归一化到[0,1]
            rgb = (rgb - rgb.min()) / (rgb.max() - rgb.min() + 1e-8)
            return np.clip(rgb, 0, 1)
        
        original_rgb = extract_rgb(results['original_hsi'])
        reflectance_rgb = extract_rgb(results['reflectance'])
        shading_rgb = extract_rgb(results['shading'])
        reconstructed_rgb = extract_rgb(results['reconstructed'])
        
        # 创建可视化
        fig, axes = plt.subplots(2, 2, figsize=(12, 12))
        
        axes[0, 0].imshow(original_rgb)
        axes[0, 0].set_title(f'原始图像 ({angle}°)')
        axes[0, 0].axis('off')
        
        axes[0, 1].imshow(reflectance_rgb)
        axes[0, 1].set_title('反射率 (本征颜色)')
        axes[0, 1].axis('off')
        
        axes[1, 0].imshow(shading_rgb)
        axes[1, 0].set_title('阴影/光照')
        axes[1, 0].axis('off')
        
        axes[1, 1].imshow(reconstructed_rgb)
        axes[1, 1].set_title('重建图像')
        axes[1, 1].axis('off')
        
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(os.path.join(save_dir, f'rgb_composite_{angle}.png'), dpi=300, bbox_inches='tight')
        plt.show()
    
    def _visualize_spectral_curves(self, results: Dict[str, np.ndarray], angle: int, save_dir: Optional[str]):
        """可视化光谱曲线"""
        # 计算中心区域的平均光谱
        H, W, B = results['original_hsi'].shape
        center_h = slice(H//4, 3*H//4)
        center_w = slice(W//4, 3*W//4)
        
        original_spectrum = np.mean(results['original_hsi'][center_h, center_w, :], axis=(0, 1))
        reflectance_spectrum = np.mean(results['reflectance'][center_h, center_w, :], axis=(0, 1))
        shading_spectrum = np.mean(results['shading'][center_h, center_w, :], axis=(0, 1))
        reconstructed_spectrum = np.mean(results['reconstructed'][center_h, center_w, :], axis=(0, 1))
        
        wavelengths = np.linspace(400, 1000, B)
        
        plt.figure(figsize=(12, 8))
        
        plt.subplot(2, 2, 1)
        plt.plot(wavelengths, original_spectrum, 'b-', linewidth=2, label='原始')
        plt.plot(wavelengths, reconstructed_spectrum, 'r--', linewidth=2, label='重建')
        plt.xlabel('波长 (nm)')
        plt.ylabel('反射率')
        plt.title(f'原始 vs 重建光谱 ({angle}°)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.subplot(2, 2, 2)
        plt.plot(wavelengths, reflectance_spectrum, 'g-', linewidth=2)
        plt.xlabel('波长 (nm)')
        plt.ylabel('反射率')
        plt.title('本征反射率光谱')
        plt.grid(True, alpha=0.3)
        
        plt.subplot(2, 2, 3)
        plt.plot(wavelengths, shading_spectrum, 'orange', linewidth=2)
        plt.xlabel('波长 (nm)')
        plt.ylabel('强度')
        plt.title('阴影/光照光谱')
        plt.grid(True, alpha=0.3)
        
        plt.subplot(2, 2, 4)
        # 计算重建误差
        error = np.abs(original_spectrum - reconstructed_spectrum)
        plt.plot(wavelengths, error, 'r-', linewidth=2)
        plt.xlabel('波长 (nm)')
        plt.ylabel('绝对误差')
        plt.title('重建误差')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(os.path.join(save_dir, f'spectral_curves_{angle}.png'), dpi=300, bbox_inches='tight')
        plt.show()
    
    def _visualize_geometry(self, results: Dict[str, np.ndarray], angle: int, save_dir: Optional[str]):
        """可视化几何信息"""
        normal_map = results['normal_map']
        coord_map = results['coord_map']
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 法向量可视化（RGB编码）
        normal_rgb = (normal_map + 1) / 2  # 从[-1,1]映射到[0,1]
        axes[0, 0].imshow(normal_rgb)
        axes[0, 0].set_title('法向量 (RGB编码)')
        axes[0, 0].axis('off')
        
        # Z坐标可视化
        z_coord = coord_map[:, :, 2]
        im1 = axes[0, 1].imshow(z_coord, cmap='viridis')
        axes[0, 1].set_title('Z坐标 (深度)')
        axes[0, 1].axis('off')
        plt.colorbar(im1, ax=axes[0, 1])
        
        # 法向量Z分量
        normal_z = normal_map[:, :, 2]
        im2 = axes[1, 0].imshow(normal_z, cmap='RdBu_r')
        axes[1, 0].set_title('法向量Z分量')
        axes[1, 0].axis('off')
        plt.colorbar(im2, ax=axes[1, 0])
        
        # 表面曲率近似（法向量变化）
        grad_normal_x = np.abs(normal_map[:, 1:, :] - normal_map[:, :-1, :])
        grad_normal_y = np.abs(normal_map[1:, :, :] - normal_map[:-1, :, :])
        curvature = np.zeros_like(normal_z)
        curvature[:, 1:] += np.sum(grad_normal_x, axis=2)
        curvature[1:, :] += np.sum(grad_normal_y, axis=2)
        
        im3 = axes[1, 1].imshow(curvature, cmap='hot')
        axes[1, 1].set_title('表面曲率近似')
        axes[1, 1].axis('off')
        plt.colorbar(im3, ax=axes[1, 1])
        
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(os.path.join(save_dir, f'geometry_{angle}.png'), dpi=300, bbox_inches='tight')
        plt.show()
    
    def _visualize_decomposition_comparison(self, results: Dict[str, np.ndarray], angle: int, save_dir: Optional[str]):
        """可视化分解结果对比"""
        # 选择几个代表性波段进行可视化
        B = results['original_hsi'].shape[2]
        band_indices = [
            int(B * 0.1),   # 近红外
            int(B * 0.3),   # 红光
            int(B * 0.5),   # 绿光
            int(B * 0.7)    # 蓝光
        ]
        
        wavelengths = [400 + (1000-400) * i / B for i in band_indices]
        
        fig, axes = plt.subplots(len(band_indices), 4, figsize=(16, 4*len(band_indices)))
        
        for i, (band_idx, wl) in enumerate(zip(band_indices, wavelengths)):
            # 原始图像
            axes[i, 0].imshow(results['original_hsi'][:, :, band_idx], cmap='viridis')
            axes[i, 0].set_title(f'原始 ({wl:.0f}nm)')
            axes[i, 0].axis('off')
            
            # 反射率
            axes[i, 1].imshow(results['reflectance'][:, :, band_idx], cmap='viridis')
            axes[i, 1].set_title(f'反射率 ({wl:.0f}nm)')
            axes[i, 1].axis('off')
            
            # 阴影
            axes[i, 2].imshow(results['shading'][:, :, band_idx], cmap='viridis')
            axes[i, 2].set_title(f'阴影 ({wl:.0f}nm)')
            axes[i, 2].axis('off')
            
            # 重建
            axes[i, 3].imshow(results['reconstructed'][:, :, band_idx], cmap='viridis')
            axes[i, 3].set_title(f'重建 ({wl:.0f}nm)')
            axes[i, 3].axis('off')
        
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(os.path.join(save_dir, f'decomposition_comparison_{angle}.png'), dpi=300, bbox_inches='tight')
        plt.show()
    
    def compare_multi_view_consistency(self, all_results: Dict[int, Dict[str, np.ndarray]], save_dir: Optional[str] = None):
        """比较多视角反射率一致性"""
        angles = list(all_results.keys())
        
        if len(angles) < 2:
            print("需要至少两个视角进行一致性比较")
            return
        
        # 计算中心区域的平均反射率光谱
        center_spectra = {}
        for angle in angles:
            results = all_results[angle]
            H, W, B = results['reflectance'].shape
            center_h = slice(H//4, 3*H//4)
            center_w = slice(W//4, 3*W//4)
            center_spectrum = np.mean(results['reflectance'][center_h, center_w, :], axis=(0, 1))
            center_spectra[angle] = center_spectrum
        
        # 可视化一致性
        wavelengths = np.linspace(400, 1000, B)
        
        plt.figure(figsize=(15, 10))
        
        # 光谱曲线对比
        plt.subplot(2, 2, 1)
        for angle, spectrum in center_spectra.items():
            plt.plot(wavelengths, spectrum, linewidth=2, label=f'{angle}°')
        plt.xlabel('波长 (nm)')
        plt.ylabel('反射率')
        plt.title('多视角反射率光谱一致性')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 计算相关系数矩阵
        plt.subplot(2, 2, 2)
        correlation_matrix = np.zeros((len(angles), len(angles)))
        for i, angle1 in enumerate(angles):
            for j, angle2 in enumerate(angles):
                corr = np.corrcoef(center_spectra[angle1], center_spectra[angle2])[0, 1]
                correlation_matrix[i, j] = corr
        
        im = plt.imshow(correlation_matrix, cmap='RdYlBu_r', vmin=0, vmax=1)
        plt.colorbar(im)
        plt.xticks(range(len(angles)), [f'{a}°' for a in angles])
        plt.yticks(range(len(angles)), [f'{a}°' for a in angles])
        plt.title('反射率光谱相关系数')
        
        # 标准差分析
        plt.subplot(2, 2, 3)
        spectra_array = np.array(list(center_spectra.values()))
        std_spectrum = np.std(spectra_array, axis=0)
        plt.plot(wavelengths, std_spectrum, 'r-', linewidth=2)
        plt.xlabel('波长 (nm)')
        plt.ylabel('标准差')
        plt.title('多视角反射率标准差')
        plt.grid(True, alpha=0.3)
        
        # 平均反射率
        plt.subplot(2, 2, 4)
        mean_spectrum = np.mean(spectra_array, axis=0)
        plt.plot(wavelengths, mean_spectrum, 'g-', linewidth=2)
        plt.fill_between(wavelengths, mean_spectrum - std_spectrum, mean_spectrum + std_spectrum, 
                        alpha=0.3, color='g')
        plt.xlabel('波长 (nm)')
        plt.ylabel('反射率')
        plt.title('平均反射率 ± 标准差')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(os.path.join(save_dir, 'multi_view_consistency.png'), dpi=300, bbox_inches='tight')
        plt.show()
        
        # 打印一致性统计
        print("\n=== 多视角一致性分析 ===")
        print(f"平均相关系数: {np.mean(correlation_matrix[np.triu_indices_from(correlation_matrix, k=1)]):.4f}")
        print(f"最小相关系数: {np.min(correlation_matrix[np.triu_indices_from(correlation_matrix, k=1)]):.4f}")
        print(f"平均标准差: {np.mean(std_spectrum):.4f}")
        print(f"最大标准差: {np.max(std_spectrum):.4f}")


def main():
    """主函数"""
    # 模型路径
    model_path = os.path.join(SAVE_DIR, 'best_model.pth')
    
    if not os.path.exists(model_path):
        print(f"模型文件不存在: {model_path}")
        print("请先训练模型或指定正确的模型路径")
        return
    
    # 创建推理引擎
    engine = InferenceEngine(model_path)
    
    # 推理所有视角
    all_results = engine.infer_all_views()
    
    # 创建结果保存目录
    results_dir = "./inference_results"
    os.makedirs(results_dir, exist_ok=True)
    
    # 可视化每个视角的结果
    for angle, results in all_results.items():
        print(f"可视化角度 {angle}° 的结果...")
        angle_dir = os.path.join(results_dir, f"angle_{angle}")
        engine.visualize_results(results, angle, angle_dir)
    
    # 多视角一致性分析
    print("进行多视角一致性分析...")
    engine.compare_multi_view_consistency(all_results, results_dir)
    
    print(f"所有结果已保存到: {results_dir}")


if __name__ == "__main__":
    main()

# -*- coding: utf-8 -*-
"""
网络模型 - 基于U-Net的双子网络架构
RES (Reflectance Estimation Subnetwork) + SES (Shading Estimation Subnetwork)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple


class DoubleConv(nn.Module):
    """双卷积层 + 批归一化 + ReLU"""
    
    def __init__(self, in_channels: int, out_channels: int, use_batch_norm: bool = True):
        super(DoubleConv, self).__init__()
        
        layers = []
        
        # 第一个卷积层
        layers.append(nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1))
        if use_batch_norm:
            layers.append(nn.BatchNorm2d(out_channels))
        layers.append(nn.ReLU(inplace=True))
        
        # 第二个卷积层
        layers.append(nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1))
        if use_batch_norm:
            layers.append(nn.BatchNorm2d(out_channels))
        layers.append(nn.ReLU(inplace=True))
        
        self.double_conv = nn.Sequential(*layers)
    
    def forward(self, x):
        return self.double_conv(x)


class Down(nn.Module):
    """下采样模块：最大池化 + 双卷积"""
    
    def __init__(self, in_channels: int, out_channels: int, use_batch_norm: bool = True):
        super(Down, self).__init__()
        self.maxpool_conv = nn.Sequential(
            nn.MaxPool2d(2),
            DoubleConv(in_channels, out_channels, use_batch_norm)
        )
    
    def forward(self, x):
        return self.maxpool_conv(x)


class Up(nn.Module):
    """上采样模块：转置卷积 + 跳跃连接 + 双卷积"""
    
    def __init__(self, in_channels: int, out_channels: int, use_batch_norm: bool = True):
        super(Up, self).__init__()
        
        # 转置卷积进行上采样
        self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)
        
        # 双卷积处理拼接后的特征
        self.conv = DoubleConv(in_channels, out_channels, use_batch_norm)
    
    def forward(self, x1, x2):
        """
        Args:
            x1: 来自下层的特征图（需要上采样）
            x2: 来自编码器的跳跃连接特征图
        """
        x1 = self.up(x1)
        
        # 处理尺寸不匹配的情况
        diffY = x2.size()[2] - x1.size()[2]
        diffX = x2.size()[3] - x1.size()[3]
        
        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2,
                        diffY // 2, diffY - diffY // 2])
        
        # 拼接特征图
        x = torch.cat([x2, x1], dim=1)
        
        return self.conv(x)


class UNet(nn.Module):
    """U-Net网络架构"""
    
    def __init__(self, 
                 in_channels: int, 
                 out_channels: int, 
                 base_channels: int = 64,
                 use_batch_norm: bool = True):
        super(UNet, self).__init__()
        
        self.in_channels = in_channels
        self.out_channels = out_channels
        
        # 编码器
        self.inc = DoubleConv(in_channels, base_channels, use_batch_norm)
        self.down1 = Down(base_channels, base_channels * 2, use_batch_norm)
        self.down2 = Down(base_channels * 2, base_channels * 4, use_batch_norm)
        self.down3 = Down(base_channels * 4, base_channels * 8, use_batch_norm)
        self.down4 = Down(base_channels * 8, base_channels * 16, use_batch_norm)
        
        # 解码器
        self.up1 = Up(base_channels * 16, base_channels * 8, use_batch_norm)
        self.up2 = Up(base_channels * 8, base_channels * 4, use_batch_norm)
        self.up3 = Up(base_channels * 4, base_channels * 2, use_batch_norm)
        self.up4 = Up(base_channels * 2, base_channels, use_batch_norm)
        
        # 输出层
        self.outc = nn.Conv2d(base_channels, out_channels, kernel_size=1)
    
    def forward(self, x):
        # 编码器路径
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)
        x5 = self.down4(x4)
        
        # 解码器路径
        x = self.up1(x5, x4)
        x = self.up2(x, x3)
        x = self.up3(x, x2)
        x = self.up4(x, x1)
        
        # 输出
        logits = self.outc(x)
        
        return logits


class IntrinsicDecompositionNet(nn.Module):
    """
    本征分解网络：双子网络架构
    RES (Reflectance Estimation Subnetwork) + SES (Shading Estimation Subnetwork)
    """
    
    def __init__(self, 
                 input_channels: int,
                 output_channels: int,
                 base_channels: int = 64,
                 use_batch_norm: bool = True):
        super(IntrinsicDecompositionNet, self).__init__()
        
        self.input_channels = input_channels
        self.output_channels = output_channels
        
        # RES: 反射率估计子网络
        self.res_net = UNet(
            in_channels=input_channels,
            out_channels=output_channels,
            base_channels=base_channels,
            use_batch_norm=use_batch_norm
        )
        
        # SES: 阴影估计子网络
        self.ses_net = UNet(
            in_channels=input_channels,
            out_channels=output_channels,
            base_channels=base_channels,
            use_batch_norm=use_batch_norm
        )
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            x: 输入特征 (B, C, H, W)
            
        Returns:
            log_reflectance: 对数域反射率 (B, output_channels, H, W)
            log_shading: 对数域阴影 (B, output_channels, H, W)
        """
        # 通过两个子网络分别估计反射率和阴影
        log_reflectance = self.res_net(x)
        log_shading = self.ses_net(x)
        
        return log_reflectance, log_shading
    
    def reconstruct_image(self, log_reflectance: torch.Tensor, log_shading: torch.Tensor) -> torch.Tensor:
        """
        重建图像：I = R ⊙ S (在对数域中为加法)
        
        Args:
            log_reflectance: 对数域反射率
            log_shading: 对数域阴影
            
        Returns:
            log_reconstructed: 对数域重建图像
        """
        return log_reflectance + log_shading
    
    def get_linear_outputs(self, log_reflectance: torch.Tensor, log_shading: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        将对数域输出转换为线性域
        
        Args:
            log_reflectance: 对数域反射率
            log_shading: 对数域阴影
            
        Returns:
            reflectance: 线性域反射率
            shading: 线性域阴影
        """
        reflectance = torch.exp(log_reflectance)
        shading = torch.exp(log_shading)
        
        return reflectance, shading


def create_model(input_channels: int, 
                output_channels: int,
                base_channels: int = 64,
                use_batch_norm: bool = True,
                device: torch.device = None) -> IntrinsicDecompositionNet:
    """
    创建模型实例
    
    Args:
        input_channels: 输入通道数
        output_channels: 输出通道数
        base_channels: 基础通道数
        use_batch_norm: 是否使用批归一化
        device: 计算设备
        
    Returns:
        model: 模型实例
    """
    model = IntrinsicDecompositionNet(
        input_channels=input_channels,
        output_channels=output_channels,
        base_channels=base_channels,
        use_batch_norm=use_batch_norm
    )
    
    if device is not None:
        model = model.to(device)
    
    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"模型创建完成:")
    print(f"  输入通道数: {input_channels}")
    print(f"  输出通道数: {output_channels}")
    print(f"  基础通道数: {base_channels}")
    print(f"  总参数数: {total_params:,}")
    print(f"  可训练参数数: {trainable_params:,}")
    print(f"  计算设备: {device}")
    
    return model


def test_model():
    """测试模型功能"""
    print("测试模型...")
    
    # 模拟输入数据
    batch_size = 2
    input_channels = 2 * 224 + 6  # 2*B + 6
    output_channels = 224  # B
    height, width = 64, 64
    
    # 创建模型
    model = create_model(input_channels, output_channels)
    
    # 创建随机输入
    x = torch.randn(batch_size, input_channels, height, width)
    
    print(f"输入形状: {x.shape}")
    
    # 前向传播
    with torch.no_grad():
        log_reflectance, log_shading = model(x)
        log_reconstructed = model.reconstruct_image(log_reflectance, log_shading)
        reflectance, shading = model.get_linear_outputs(log_reflectance, log_shading)
    
    print(f"输出形状:")
    print(f"  对数域反射率: {log_reflectance.shape}")
    print(f"  对数域阴影: {log_shading.shape}")
    print(f"  对数域重建: {log_reconstructed.shape}")
    print(f"  线性域反射率: {reflectance.shape}")
    print(f"  线性域阴影: {shading.shape}")
    
    print("✓ 模型测试通过")


if __name__ == "__main__":
    test_model()

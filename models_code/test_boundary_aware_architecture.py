#!/usr/bin/env python3
"""
测试边界感知网络架构
"""

import sys
import os
import torch

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from train import Trainer
from config import *

def test_boundary_aware_architecture():
    """测试边界感知网络架构"""
    print("🧪 测试边界感知网络架构")
    print("=" * 60)
    
    try:
        # 测试1: 传统U-Net + 传统损失
        print("1. 测试传统U-Net + 传统损失...")
        trainer1 = Trainer(
            use_boundary_aware_loss=False,
            use_boundary_aware_model=False
        )
        print("✅ 传统组合创建成功")
        
        # 测试2: 传统U-Net + 边界感知损失
        print("\n2. 测试传统U-Net + 边界感知损失...")
        trainer2 = Trainer(
            use_boundary_aware_loss=True,
            use_boundary_aware_model=False
        )
        print("✅ 混合组合1创建成功")
        
        # 测试3: 边界感知网络 + 传统损失
        print("\n3. 测试边界感知网络 + 传统损失...")
        trainer3 = Trainer(
            use_boundary_aware_loss=False,
            use_boundary_aware_model=True
        )
        print("✅ 混合组合2创建成功")
        
        # 测试4: 边界感知网络 + 边界感知损失
        print("\n4. 测试边界感知网络 + 边界感知损失...")
        trainer4 = Trainer(
            use_boundary_aware_loss=True,
            use_boundary_aware_model=True
        )
        print("✅ 完全边界感知组合创建成功")
        
        # 测试前向传播
        print("\n5. 测试前向传播...")
        
        # 创建模拟数据
        batch_size = 2
        input_channels = 2 * 224 + 6  # 2*B + 6
        height, width = 84, 75
        
        # 模拟输入数据（确保在正确设备上）
        device = trainer4.device
        mock_batch = {
            'hsi1': torch.randn(batch_size, 224, height, width).to(device),
            'hsi2': torch.randn(batch_size, 224, height, width).to(device),
            'log_hsi1': torch.randn(batch_size, 224, height, width).to(device),
            'log_hsi2': torch.randn(batch_size, 224, height, width).to(device),
            'normal1': torch.randn(batch_size, 3, height, width).to(device),
            'normal2': torch.randn(batch_size, 3, height, width).to(device),
        }

        # 构建输入特征
        input1 = torch.cat([
            mock_batch['log_hsi1'],
            mock_batch['log_hsi2'],
            mock_batch['normal1'],
            mock_batch['normal2']
        ], dim=1)
        
        # 测试边界感知网络的前向传播
        trainer4.model.eval()
        with torch.no_grad():
            log_reflectance, log_shading = trainer4.model(input1)
            
        print(f"✅ 前向传播成功:")
        print(f"   输入形状: {input1.shape}")
        print(f"   反射率输出形状: {log_reflectance.shape}")
        print(f"   阴影输出形状: {log_shading.shape}")
        
        # 比较参数数量
        print("\n6. 参数数量对比:")
        
        def count_parameters(model):
            return sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        traditional_params = count_parameters(trainer1.model)
        boundary_aware_params = count_parameters(trainer4.model)
        
        print(f"   传统U-Net参数数量: {traditional_params:,}")
        print(f"   边界感知网络参数数量: {boundary_aware_params:,}")
        print(f"   参数增加比例: {boundary_aware_params/traditional_params:.2f}x")
        
        print("\n✅ 所有测试通过！边界感知网络架构已成功集成。")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    success = test_boundary_aware_architecture()
    
    if success:
        print("\n🎉 架构测试成功！")
        print("\n📋 使用说明:")
        print("现在你可以运行 'python train.py' 并选择:")
        print("1. 网络架构:")
        print("   - 选择 '1' 使用传统U-Net架构")
        print("   - 选择 '2' 使用边界感知网络架构 (推荐)")
        print("2. 损失函数:")
        print("   - 选择 '1' 使用传统损失函数")
        print("   - 选择 '2' 使用边界感知损失函数")
        print("\n🎯 推荐组合:")
        print("   - 方案1: 边界感知网络 + 传统损失 (纯架构改进)")
        print("   - 方案2: 边界感知网络 + 边界感知损失 (完全边界感知)")
        print("\n这样可以验证是网络架构还是损失函数对边界效应的影响更大！")
    else:
        print("\n❌ 架构测试失败，请检查错误信息")


if __name__ == "__main__":
    main()

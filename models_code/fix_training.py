# -*- coding: utf-8 -*-
"""
修复训练脚本 - 解决可视化中的梯度问题
"""

import torch
import os
from train import Trainer
from config import *


def resume_training():
    """恢复训练并修复可视化问题"""
    print("=" * 60)
    print("🔧 修复训练脚本并恢复训练")
    print("=" * 60)
    
    # 验证配置
    validate_config()
    
    # 创建训练器
    trainer = Trainer()
    
    # 检查是否有已保存的检查点
    latest_checkpoint = os.path.join(SAVE_DIR, 'latest_checkpoint.pth')
    if os.path.exists(latest_checkpoint):
        print(f"发现检查点文件: {latest_checkpoint}")
        trainer.load_checkpoint(latest_checkpoint)
        print(f"已从epoch {trainer.current_epoch + 1}恢复训练")
    else:
        print("没有找到检查点文件，从头开始训练")
    
    print("\n📊 可视化功能已修复:")
    print(f"  • 已添加torch.no_grad()上下文")
    print(f"  • 已添加.detach()调用")
    print(f"  • 可视化频率: 每 {VIS_FREQUENCY} 个epoch")
    
    print("\n🔗 监控链接:")
    print(f"  • TensorBoard: http://localhost:6007")
    print(f"  • 可视化文件: {os.path.abspath(VIS_DIR)}")
    
    print("\n" + "=" * 60)
    print("继续训练...")
    print("=" * 60)
    
    try:
        # 开始训练
        trainer.train()
        
        print("\n" + "=" * 60)
        print("🎉 训练完成！")
        print("=" * 60)
        print(f"📁 结果保存位置:")
        print(f"  • 模型权重: {SAVE_DIR}")
        print(f"  • 可视化结果: {VIS_DIR}")
        
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        print("请检查错误信息并重新运行")


if __name__ == "__main__":
    resume_training()

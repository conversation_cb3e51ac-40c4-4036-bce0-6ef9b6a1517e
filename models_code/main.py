# -*- coding: utf-8 -*-
"""
主运行脚本 - 基于多模态数据与物理先验的苹果本征高光谱特性提取模型
"""

import argparse
import os
import sys

from config import *
from train import Trainer
from inference import InferenceEngine


def train_model():
    """训练模型"""
    print("=" * 60)
    print("开始训练模型")
    print("=" * 60)

    # 验证配置
    validate_config()

    # 创建训练器并开始训练
    trainer = Trainer()

    # 检查是否有已保存的检查点
    latest_checkpoint = os.path.join(SAVE_DIR, 'latest_checkpoint.pth')
    if os.path.exists(latest_checkpoint):
        print(f"发现检查点文件: {latest_checkpoint}")
        response = input("是否从检查点恢复训练? (y/n): ")
        if response.lower() == 'y':
            trainer.load_checkpoint(latest_checkpoint)

    # 开始训练
    trainer.train()


def train_model_with_visualization():
    """带可视化的训练模型"""
    print("=" * 60)
    print("🚀 启动带可视化的训练")
    print("=" * 60)

    # 验证配置
    validate_config()

    # 创建训练器
    trainer = Trainer()

    # 检查是否有已保存的检查点
    latest_checkpoint = os.path.join(SAVE_DIR, 'latest_checkpoint.pth')
    if os.path.exists(latest_checkpoint):
        print(f"发现检查点文件: {latest_checkpoint}")
        response = input("是否从检查点恢复训练? (y/n): ")
        if response.lower() == 'y':
            trainer.load_checkpoint(latest_checkpoint)

    print("\n📊 可视化功能已启用:")
    print(f"  • 分解结果可视化: 每 {VIS_FREQUENCY} 个epoch")
    print(f"  • 损失曲线可视化: 每 {VIS_FREQUENCY} 个epoch")
    print(f"  • 训练摘要: 每 {VIS_FREQUENCY} 个epoch")
    print(f"  • 可视化保存目录: {VIS_DIR}")

    print("\n🔗 监控链接:")
    print(f"  • 可视化文件: {os.path.abspath(VIS_DIR)}")

    print("\n" + "=" * 60)
    print("开始训练...")
    print("=" * 60)

    # 开始训练
    trainer.train()

    print("\n" + "=" * 60)
    print("🎉 训练完成！")
    print("=" * 60)
    print(f"📁 结果保存位置:")
    print(f"  • 模型权重: {SAVE_DIR}")
    print(f"  • 可视化结果: {VIS_DIR}")


def test_visualization():
    """测试可视化功能"""
    print("=" * 60)
    print("测试可视化功能")
    print("=" * 60)

    try:
        from test_visualizer import test_visualizer
        test_visualizer()
        print("✓ 可视化功能测试通过")
    except Exception as e:
        print(f"❌ 可视化功能测试失败: {e}")


def run_inference():
    """运行推理"""
    print("=" * 60)
    print("开始模型推理")
    print("=" * 60)
    
    # 检查模型文件
    model_path = os.path.join(SAVE_DIR, 'best_model.pth')
    
    if not os.path.exists(model_path):
        print(f"模型文件不存在: {model_path}")
        print("请先训练模型或指定正确的模型路径")
        
        # 检查是否有其他可用的模型文件
        if os.path.exists(SAVE_DIR):
            model_files = [f for f in os.listdir(SAVE_DIR) if f.endswith('.pth')]
            if model_files:
                print(f"发现以下模型文件: {model_files}")
                model_file = input("请输入要使用的模型文件名: ")
                model_path = os.path.join(SAVE_DIR, model_file)
                if not os.path.exists(model_path):
                    print(f"指定的模型文件不存在: {model_path}")
                    return
            else:
                print("没有找到任何模型文件")
                return
        else:
            print("模型保存目录不存在")
            return
    
    # 创建推理引擎
    engine = InferenceEngine(model_path)
    
    # 推理所有视角
    all_results = engine.infer_all_views()
    
    # 创建结果保存目录
    results_dir = "./inference_results"
    os.makedirs(results_dir, exist_ok=True)
    
    # 可视化每个视角的结果
    for angle, results in all_results.items():
        print(f"可视化角度 {angle}° 的结果...")
        angle_dir = os.path.join(results_dir, f"angle_{angle}")
        engine.visualize_results(results, angle, angle_dir)
    
    # 多视角一致性分析
    print("进行多视角一致性分析...")
    engine.compare_multi_view_consistency(all_results, results_dir)
    
    print(f"所有结果已保存到: {results_dir}")


def test_data_loading():
    """测试数据加载"""
    print("=" * 60)
    print("测试数据加载")
    print("=" * 60)
    
    try:
        from dataset import test_dataset
        test_dataset()
        print("✓ 数据加载测试通过")
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")


def test_model():
    """测试模型"""
    print("=" * 60)
    print("测试模型")
    print("=" * 60)
    
    try:
        from model import test_model
        test_model()
        print("✓ 模型测试通过")
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")


def test_loss_functions():
    """测试损失函数"""
    print("=" * 60)
    print("测试损失函数")
    print("=" * 60)
    
    try:
        from loss import test_loss_functions
        test_loss_functions()
        print("✓ 损失函数测试通过")
    except Exception as e:
        print(f"❌ 损失函数测试失败: {e}")


def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("运行所有测试")
    print("=" * 60)
    
    test_data_loading()
    print()
    test_model()
    print()
    test_loss_functions()
    
    print("=" * 60)
    print("所有测试完成")
    print("=" * 60)


def check_environment():
    """检查环境和依赖"""
    print("=" * 60)
    print("检查环境和依赖")
    print("=" * 60)
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查必要的包
    required_packages = [
        'torch', 'numpy', 'pandas', 'matplotlib',
        'open3d', 'scipy', 'tqdm'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"❌ {package} (未安装)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少以下包: {missing_packages}")
        print("请使用以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
    else:
        print("\n✓ 所有必要的包都已安装")
    
    # 检查数据目录
    print(f"\n数据目录: {DATA_DIR}")
    if os.path.exists(DATA_DIR):
        print("✓ 数据目录存在")
        
        # 检查数据文件
        missing_files = []
        for angle in ANGLES:
            csv_file = os.path.join(DATA_DIR, f"hsi_point_fusion_{angle}_with_normals_oriented.csv")
            if os.path.exists(csv_file):
                print(f"✓ {angle}° 数据文件存在")
            else:
                print(f"❌ {angle}° 数据文件不存在")
                missing_files.append(csv_file)
        
        if missing_files:
            print(f"\n缺少以下数据文件:")
            for file in missing_files:
                print(f"  {file}")
        else:
            print("\n✓ 所有数据文件都存在")
    else:
        print("❌ 数据目录不存在")
        print(f"请确保数据目录存在: {DATA_DIR}")
    
    # 检查GPU
    try:
        import torch
        if torch.cuda.is_available():
            print(f"\n✓ GPU可用: {torch.cuda.get_device_name()}")
            print(f"  GPU数量: {torch.cuda.device_count()}")
            print(f"  当前GPU: {torch.cuda.current_device()}")
        elif torch.backends.mps.is_available():
            print("\n✓ Apple Silicon GPU可用")
        else:
            print("\n⚠ 只有CPU可用，训练可能会很慢")
    except:
        print("\n❌ 无法检查GPU状态")


def print_help():
    """打印帮助信息"""
    print("=" * 60)
    print("基于多模态数据与物理先验的苹果本征高光谱特性提取模型")
    print("=" * 60)
    print()
    print("可用命令:")
    print("  train          - 训练模型")
    print("  train-vis      - 带可视化的训练模型")
    print("  inference      - 运行推理")
    print("  test-data      - 测试数据加载")
    print("  test-model     - 测试模型")
    print("  test-loss      - 测试损失函数")
    print("  test-vis       - 测试可视化功能")
    print("  test-all       - 运行所有测试")
    print("  check-env      - 检查环境和依赖")
    print("  help           - 显示此帮助信息")
    print()
    print("使用示例:")
    print("  python main.py train-vis   # 推荐：带实时可视化的训练")
    print("  python main.py train       # 普通训练")
    print("  python main.py inference   # 运行推理")
    print("  python main.py test-vis    # 测试可视化")
    print("  python main.py check-env   # 检查环境")
    print()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='苹果本征高光谱特性提取模型')
    parser.add_argument('command',
                       choices=['train', 'train-vis', 'inference', 'test-data', 'test-model',
                               'test-loss', 'test-vis', 'test-all', 'check-env', 'help'],
                       help='要执行的命令')
    
    # 如果没有提供参数，显示帮助
    if len(sys.argv) == 1:
        print_help()
        return
    
    args = parser.parse_args()
    
    # 执行对应的命令
    if args.command == 'train':
        train_model()
    elif args.command == 'train-vis':
        train_model_with_visualization()
    elif args.command == 'inference':
        run_inference()
    elif args.command == 'test-data':
        test_data_loading()
    elif args.command == 'test-model':
        test_model()
    elif args.command == 'test-loss':
        test_loss_functions()
    elif args.command == 'test-vis':
        test_visualization()
    elif args.command == 'test-all':
        run_all_tests()
    elif args.command == 'check-env':
        check_environment()
    elif args.command == 'help':
        print_help()


if __name__ == "__main__":
    main()

# -*- coding: utf-8 -*-
"""
测试可视化功能
"""

import torch
import numpy as np
from config import *
from dataset import create_data_loaders
from model import create_model
from visualizer import create_visualizer


def test_visualizer():
    """测试可视化器功能"""
    print("测试可视化器...")
    
    # 创建数据加载器
    train_loader, val_loader = create_data_loaders(
        data_dir=DATA_DIR,
        angles=ANGLES,
        batch_size=1,  # 使用小批次进行测试
        val_ratio=0.2,
        num_workers=0
    )
    
    # 获取一个样本
    sample_dataset = train_loader.dataset.dataset
    input_channels = sample_dataset.get_input_channels()
    output_channels = sample_dataset.get_output_channels()
    
    # 创建模型
    device = get_device()
    model = create_model(
        input_channels=input_channels,
        output_channels=output_channels,
        base_channels=BASE_CHANNELS,
        use_batch_norm=USE_BATCH_NORM,
        device=device
    )
    
    # 加载训练好的模型权重
    checkpoint_path = './saved_models/latest_checkpoint.pth'
    if os.path.exists(checkpoint_path):
        checkpoint = torch.load(checkpoint_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"已加载模型权重，epoch: {checkpoint['epoch']}")
    else:
        print("未找到模型权重，使用随机初始化")
    
    model.eval()
    
    # 创建可视化器
    visualizer = create_visualizer()
    
    # 获取一个批次的数据
    for batch_idx, batch in enumerate(train_loader):
        # 将数据移到设备
        batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v 
                for k, v in batch.items()}
        
        # 前向传播
        with torch.no_grad():
            input1 = batch['input1']
            log_reflectance1, log_shading1 = model(input1)
        
        # 可视化分解结果
        print("生成分解结果可视化...")
        visualizer.visualize_decomposition_results(
            batch, log_reflectance1, log_shading1, 
            epoch=999, batch_idx=batch_idx, sample_idx=0
        )
        
        # 只测试第一个批次
        break
    
    # 测试损失曲线可视化
    print("生成损失曲线可视化...")
    
    # 模拟损失历史
    train_losses = [50.0, 45.0, 40.0, 35.0, 32.0, 30.0, 28.0, 26.0, 25.0, 24.0]
    val_losses = [55.0, 48.0, 42.0, 38.0, 35.0, 33.0, 31.0, 29.0, 28.0, 27.0]
    loss_details = {
        'reconstruction_loss': [45.0, 40.0, 35.0, 30.0, 27.0, 25.0, 23.0, 21.0, 20.0, 19.0],
        'smoothness_loss': [2.0, 2.1, 2.0, 1.9, 1.8, 1.7, 1.6, 1.5, 1.4, 1.3],
        'normal_consistency_loss': [1.5, 1.4, 1.3, 1.2, 1.1, 1.0, 0.9, 0.8, 0.7, 0.6],
        'reflectance_consistency_loss': [1.5, 1.5, 1.7, 2.0, 2.2, 2.3, 2.5, 2.7, 2.9, 3.1]
    }
    
    visualizer.visualize_loss_curves(
        train_losses, val_losses, loss_details, epoch=10
    )
    
    # 测试训练摘要
    print("生成训练摘要...")
    visualizer.create_training_summary(
        epoch=10,
        train_loss=24.0,
        val_loss=27.0,
        best_val_loss=25.0,
        learning_rate=1e-4
    )
    
    print("✓ 可视化器测试完成！")
    print(f"结果保存在: {VIS_DIR}")


if __name__ == "__main__":
    test_visualizer()

# -*- coding: utf-8 -*-
"""
数据集类 - 多视角高光谱数据加载和预处理
"""

import os
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from typing import Dict, List, Tuple, Optional
import random
from itertools import combinations

from utils import (
    get_image_dims_from_csv, 
    load_raw_data_from_csv, 
    create_map_from_points,
    calculate_r_init_from_45_degree,
    smooth_feature_map
)
from config import *


class MultiViewHSIDataset(Dataset):
    """
    多视角高光谱数据集
    每个样本包含两个不同视角的数据，用于训练一致性约束
    """
    
    def __init__(self, 
                 data_dir: str,
                 angles: List[int],
                 reference_angle: int = 45,
                 roi_ratio: float = 0.2,
                 log_epsilon: float = 1e-6,
                 use_smoothing: bool = True,
                 smoothing_sigma: float = 1.0):
        """
        初始化数据集
        
        Args:
            data_dir: 数据目录路径
            angles: 可用角度列表
            reference_angle: 参考角度（用于计算R_init）
            roi_ratio: R_init计算时的中心区域比例
            log_epsilon: 对数变换的小值
            use_smoothing: 是否对特征图进行平滑
            smoothing_sigma: 平滑参数
        """
        self.data_dir = data_dir
        self.angles = angles
        self.reference_angle = reference_angle
        self.roi_ratio = roi_ratio
        self.log_epsilon = log_epsilon
        self.use_smoothing = use_smoothing
        self.smoothing_sigma = smoothing_sigma
        
        # 确定图像尺寸
        self._determine_image_dimensions()
        
        # 加载所有角度的数据
        self._load_all_data()
        
        # 计算R_init
        self._calculate_r_init()
        
        # 生成视角对
        self._generate_view_pairs()
        
        print(f"数据集初始化完成:")
        print(f"  图像尺寸: {self.H} × {self.W}")
        print(f"  波段数: {self.B}")
        print(f"  可用角度: {self.angles}")
        print(f"  视角对数量: {len(self.view_pairs)}")
    
    def _determine_image_dimensions(self):
        """确定图像尺寸"""
        # 使用第一个可用角度的数据确定图像尺寸
        first_angle = self.angles[0]
        csv_path = os.path.join(self.data_dir, f"hsi_point_fusion_{first_angle}_with_normals_oriented.csv")
        
        self.H, self.W = get_image_dims_from_csv(csv_path)
        self.B = NUM_BANDS
        
        # 更新全局配置
        global IMG_HEIGHT, IMG_WIDTH
        IMG_HEIGHT = self.H
        IMG_WIDTH = self.W
    
    def _load_all_data(self):
        """加载所有角度的数据"""
        self.data = {}
        
        for angle in self.angles:
            csv_path = os.path.join(self.data_dir, f"hsi_point_fusion_{angle}_with_normals_oriented.csv")
            
            if not os.path.exists(csv_path):
                print(f"警告: 文件不存在 {csv_path}")
                continue
            
            # 加载原始数据
            pixel_coords, point_cloud_data, normal_data, hsi_data = load_raw_data_from_csv(csv_path)
            
            # 创建特征图
            hsi_map = create_map_from_points(pixel_coords, hsi_data, self.H, self.W, self.B)
            normal_map = create_map_from_points(pixel_coords, normal_data, self.H, self.W, 3)
            coord_map = create_map_from_points(pixel_coords, point_cloud_data, self.H, self.W, 3)
            
            # 可选的平滑处理
            if self.use_smoothing:
                hsi_map = smooth_feature_map(hsi_map, self.smoothing_sigma)
                normal_map = smooth_feature_map(normal_map, self.smoothing_sigma)
                coord_map = smooth_feature_map(coord_map, self.smoothing_sigma)
            
            # 存储数据
            self.data[angle] = {
                'hsi_map': hsi_map,
                'normal_map': normal_map,
                'coord_map': coord_map,
                'pixel_coords': pixel_coords,
                'point_cloud_data': point_cloud_data,
                'normal_data': normal_data,
                'hsi_data': hsi_data
            }
            
            print(f"加载角度 {angle}° 数据完成")
    
    def _calculate_r_init(self):
        """计算R_init参考反射率"""
        try:
            self.r_init, self.reference_spectrum = calculate_r_init_from_45_degree(
                self.data_dir, self.angles, self.H, self.W, self.B, self.roi_ratio
            )
            print("✓ R_init计算完成")
        except Exception as e:
            print(f"❌ R_init计算失败: {e}")
            # 使用默认值
            self.reference_spectrum = np.ones(self.B) * 0.5
            self.r_init = np.tile(self.reference_spectrum.reshape(1, 1, self.B), (self.H, self.W, 1))
            print("使用默认R_init值")
    
    def _generate_view_pairs(self):
        """生成所有可能的视角对"""
        available_angles = list(self.data.keys())
        self.view_pairs = list(combinations(available_angles, 2))
        
        # 确保有足够的训练样本，可以重复某些对
        if len(self.view_pairs) < 10:
            # 如果视角对太少，重复一些对
            self.view_pairs = self.view_pairs * (10 // len(self.view_pairs) + 1)
        
        random.shuffle(self.view_pairs)
    
    def __len__(self):
        """返回数据集大小"""
        return len(self.view_pairs)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        获取一个训练样本
        
        Args:
            idx: 样本索引
            
        Returns:
            sample: 包含两个视角数据的字典
        """
        angle1, angle2 = self.view_pairs[idx]
        
        # 获取两个视角的数据
        data1 = self.data[angle1]
        data2 = self.data[angle2]
        
        # 构建输入特征
        # 对于每个视角: [HSI, Normal, Coord, R_init] -> (H, W, B+3+3+B)
        input1 = self._build_input_features(data1)
        input2 = self._build_input_features(data2)
        
        # 转换为PyTorch张量并调整维度顺序 (H, W, C) -> (C, H, W)
        sample = {
            'input1': torch.from_numpy(input1).permute(2, 0, 1).float(),
            'input2': torch.from_numpy(input2).permute(2, 0, 1).float(),
            'hsi1': torch.from_numpy(data1['hsi_map']).permute(2, 0, 1).float(),
            'hsi2': torch.from_numpy(data2['hsi_map']).permute(2, 0, 1).float(),
            'normal1': torch.from_numpy(data1['normal_map']).permute(2, 0, 1).float(),
            'normal2': torch.from_numpy(data2['normal_map']).permute(2, 0, 1).float(),
            'coord1': torch.from_numpy(data1['coord_map']).permute(2, 0, 1).float(),
            'coord2': torch.from_numpy(data2['coord_map']).permute(2, 0, 1).float(),
            'r_init': torch.from_numpy(self.r_init).permute(2, 0, 1).float(),
            'angle1': angle1,
            'angle2': angle2
        }
        
        return sample
    
    def _build_input_features(self, data: Dict[str, np.ndarray]) -> np.ndarray:
        """
        构建网络输入特征
        
        Args:
            data: 单个视角的数据字典
            
        Returns:
            input_features: (H, W, total_channels) 输入特征
        """
        hsi_map = data['hsi_map']  # (H, W, B)
        normal_map = data['normal_map']  # (H, W, 3)
        coord_map = data['coord_map']  # (H, W, 3)
        
        # 对数变换HSI数据以提高数值稳定性
        log_hsi = np.log(hsi_map + self.log_epsilon)
        log_r_init = np.log(self.r_init + self.log_epsilon)
        
        # 拼接所有特征: [log_HSI, Normal, Coord, log_R_init]
        input_features = np.concatenate([
            log_hsi,      # (H, W, B)
            normal_map,   # (H, W, 3)
            coord_map,    # (H, W, 3)
            log_r_init    # (H, W, B)
        ], axis=2)  # (H, W, 2*B+6)
        
        return input_features
    
    def get_input_channels(self) -> int:
        """返回输入特征的通道数"""
        return 2 * self.B + 6  # 2*B (HSI + R_init) + 3 (Normal) + 3 (Coord)
    
    def get_output_channels(self) -> int:
        """返回输出的通道数"""
        return self.B  # 反射率或阴影的通道数


def create_data_loaders(data_dir: str,
                       angles: List[int],
                       batch_size: int = 2,
                       val_ratio: float = 0.2,
                       num_workers: int = 4) -> Tuple[DataLoader, DataLoader]:
    """
    创建训练和验证数据加载器
    
    Args:
        data_dir: 数据目录
        angles: 角度列表
        batch_size: 批次大小
        val_ratio: 验证集比例
        num_workers: 数据加载进程数
        
    Returns:
        train_loader, val_loader: 训练和验证数据加载器
    """
    # 创建完整数据集
    full_dataset = MultiViewHSIDataset(data_dir, angles)
    
    # 划分训练集和验证集
    dataset_size = len(full_dataset)
    val_size = int(dataset_size * val_ratio)
    train_size = dataset_size - val_size
    
    train_dataset, val_dataset = torch.utils.data.random_split(
        full_dataset, [train_size, val_size]
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    
    print(f"数据加载器创建完成:")
    print(f"  训练样本: {train_size}")
    print(f"  验证样本: {val_size}")
    print(f"  批次大小: {batch_size}")
    
    return train_loader, val_loader


def test_dataset():
    """测试数据集功能"""
    print("测试数据集...")
    
    try:
        # 创建数据集
        dataset = MultiViewHSIDataset(DATA_DIR, ANGLES)
        
        # 测试获取样本
        sample = dataset[0]
        
        print("样本数据形状:")
        for key, value in sample.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape}")
            else:
                print(f"  {key}: {value}")
        
        print("✓ 数据集测试通过")
        
    except Exception as e:
        print(f"❌ 数据集测试失败: {e}")


if __name__ == "__main__":
    test_dataset()

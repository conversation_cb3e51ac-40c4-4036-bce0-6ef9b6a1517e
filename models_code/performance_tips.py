# -*- coding: utf-8 -*-
"""
训练性能优化建议和监控
"""

import time
import psutil
import torch
from config import *


def print_performance_tips():
    """打印性能优化建议"""
    print("=" * 60)
    print("🚀 训练性能优化建议")
    print("=" * 60)
    
    print("\n📊 当前配置分析:")
    print(f"  • 检查点保存频率: 每 {CHECKPOINT_FREQUENCY} epoch")
    print(f"  • 验证频率: 每 {VAL_FREQUENCY} epoch")
    print(f"  • 可视化频率: 每 {VIS_FREQUENCY} epoch")
    print(f"  • 批次大小: {BATCH_SIZE}")
    
    print("\n⚡ 性能瓶颈分析:")
    print("  1. 模型保存 (I/O操作)")
    print("     - 每个epoch都保存检查点会很慢")
    print("     - 建议: 减少保存频率到20-50 epoch")
    
    print("\n  2. 可视化生成")
    print("     - matplotlib图像生成较慢")
    print("     - 建议: 减少可视化频率或异步生成")
    
    print("\n  3. 验证过程")
    print("     - 验证需要遍历整个验证集")
    print("     - 建议: 适当增加验证频率")
    
    print("\n🔧 优化建议:")
    print("  1. 减少检查点保存频率:")
    print("     CHECKPOINT_FREQUENCY = 20-50")
    
    print("\n  2. 减少可视化频率:")
    print("     VIS_FREQUENCY = 50-100")
    
    print("\n  3. 增加验证频率:")
    print("     VAL_FREQUENCY = 10-20")
    
    print("\n  4. 使用更大的批次大小:")
    print("     BATCH_SIZE = 4-8 (如果GPU内存允许)")


def monitor_system_resources():
    """监控系统资源使用"""
    print("\n💻 系统资源监控:")
    
    # CPU使用率
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"  • CPU使用率: {cpu_percent:.1f}%")
    
    # 内存使用
    memory = psutil.virtual_memory()
    print(f"  • 内存使用: {memory.percent:.1f}% ({memory.used/1024**3:.1f}GB/{memory.total/1024**3:.1f}GB)")
    
    # GPU信息
    if torch.cuda.is_available():
        print(f"  • GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            gpu_used = torch.cuda.memory_allocated(i) / 1024**3
            print(f"  • GPU {i}: {gpu_used:.1f}GB/{gpu_memory:.1f}GB")
    elif torch.backends.mps.is_available():
        print("  • 使用Apple Silicon GPU (MPS)")
    else:
        print("  • 使用CPU计算")


def suggest_optimal_config():
    """建议最优配置"""
    print("\n⚙️ 建议的快速训练配置:")
    print("```python")
    print("# 快速训练配置")
    print("CHECKPOINT_FREQUENCY = 50  # 减少保存频率")
    print("VIS_FREQUENCY = 100        # 减少可视化频率")
    print("VAL_FREQUENCY = 20         # 增加验证间隔")
    print("BATCH_SIZE = 4             # 增加批次大小(如果内存允许)")
    print("```")
    
    print("\n📈 预期性能提升:")
    print("  • 减少I/O操作: ~30-50%时间节省")
    print("  • 减少可视化: ~20-30%时间节省")
    print("  • 优化批次大小: ~10-20%速度提升")


class TrainingTimer:
    """训练时间监控器"""
    
    def __init__(self):
        self.epoch_times = []
        self.validation_times = []
        self.visualization_times = []
        self.save_times = []
    
    def start_epoch(self):
        self.epoch_start = time.time()
    
    def end_epoch(self):
        epoch_time = time.time() - self.epoch_start
        self.epoch_times.append(epoch_time)
        return epoch_time
    
    def time_validation(self, func):
        start = time.time()
        result = func()
        validation_time = time.time() - start
        self.validation_times.append(validation_time)
        return result, validation_time
    
    def time_visualization(self, func):
        start = time.time()
        result = func()
        viz_time = time.time() - start
        self.visualization_times.append(viz_time)
        return result, viz_time
    
    def time_save(self, func):
        start = time.time()
        result = func()
        save_time = time.time() - start
        self.save_times.append(save_time)
        return result, save_time
    
    def print_summary(self):
        if self.epoch_times:
            avg_epoch = sum(self.epoch_times) / len(self.epoch_times)
            print(f"\n⏱️ 时间统计:")
            print(f"  • 平均每epoch: {avg_epoch:.2f}秒")
            
            if self.validation_times:
                avg_val = sum(self.validation_times) / len(self.validation_times)
                print(f"  • 平均验证时间: {avg_val:.2f}秒")
            
            if self.visualization_times:
                avg_viz = sum(self.visualization_times) / len(self.visualization_times)
                print(f"  • 平均可视化时间: {avg_viz:.2f}秒")
            
            if self.save_times:
                avg_save = sum(self.save_times) / len(self.save_times)
                print(f"  • 平均保存时间: {avg_save:.2f}秒")


def main():
    """主函数"""
    print_performance_tips()
    monitor_system_resources()
    suggest_optimal_config()
    
    print("\n" + "=" * 60)
    print("💡 使用建议:")
    print("1. 先用快速配置训练几个epoch测试")
    print("2. 根据实际情况调整参数")
    print("3. 在重要的milestone增加保存和可视化频率")
    print("=" * 60)


if __name__ == "__main__":
    main()

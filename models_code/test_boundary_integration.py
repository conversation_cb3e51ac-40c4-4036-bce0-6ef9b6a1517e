#!/usr/bin/env python3
"""
测试边界感知训练集成
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from train import Trainer, BoundaryAwareLoss
from config import *

def test_boundary_aware_integration():
    """测试边界感知训练集成"""
    print("🧪 测试边界感知训练集成")
    print("=" * 50)
    
    try:
        # 测试传统训练器
        print("1. 测试传统训练器...")
        trainer_traditional = Trainer(use_boundary_aware=False)
        print("✅ 传统训练器创建成功")
        
        # 测试边界感知训练器
        print("\n2. 测试边界感知训练器...")
        trainer_boundary = Trainer(use_boundary_aware=True)
        print("✅ 边界感知训练器创建成功")
        
        # 测试损失函数
        print("\n3. 测试边界感知损失函数...")
        boundary_loss = BoundaryAwareLoss()
        print("✅ 边界感知损失函数创建成功")
        
        # 检查损失详情
        print("\n4. 检查损失详情配置...")
        print("传统训练器损失详情:", list(trainer_traditional.loss_details.keys()))
        print("边界感知训练器损失详情:", list(trainer_boundary.loss_details.keys()))
        
        # 验证边界感知训练器有额外的损失项
        if 'edge_preserving_loss' in trainer_boundary.loss_details:
            print("✅ 边界感知训练器正确包含边界保持损失")
        else:
            print("❌ 边界感知训练器缺少边界保持损失")
            
        if 'edge_preserving_loss' not in trainer_traditional.loss_details:
            print("✅ 传统训练器正确排除边界保持损失")
        else:
            print("❌ 传统训练器错误包含边界保持损失")
        
        print("\n✅ 所有测试通过！边界感知训练已成功集成到主训练流程中。")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    success = test_boundary_aware_integration()
    
    if success:
        print("\n🎉 集成测试成功！")
        print("\n📋 使用说明:")
        print("1. 运行 'python train.py' 开始训练")
        print("2. 选择训练模式:")
        print("   - 选择 '1' 使用传统训练模式")
        print("   - 选择 '2' 使用边界感知训练模式 (推荐)")
        print("3. 边界感知模式将自动:")
        print("   - 降低平滑性损失权重 (0.1 → 0.05)")
        print("   - 添加边界保持损失 (权重 0.3)")
        print("   - 在训练过程中显示边界损失")
    else:
        print("\n❌ 集成测试失败，请检查错误信息")


if __name__ == "__main__":
    main()

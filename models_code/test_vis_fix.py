# -*- coding: utf-8 -*-
"""
测试可视化修复
"""

import numpy as np
from visualizer import create_visualizer


def test_loss_curves_fix():
    """测试损失曲线修复"""
    print("测试损失曲线可视化修复...")
    
    # 创建可视化器
    visualizer = create_visualizer()
    
    # 模拟不匹配的数据长度（这是导致错误的原因）
    train_losses = [50.0, 45.0, 40.0, 35.0, 32.0, 30.0, 28.0, 26.0, 25.0, 24.0, 
                   22.0, 21.0, 20.0, 19.0, 18.0, 17.0, 16.0, 15.0, 14.5, 14.0, 13.8, 13.5]  # 22个元素
    
    val_losses = [55.0, 48.0, 42.0, 38.0, 35.0]  # 只有5个元素（验证频率为5）
    
    # 模拟详细损失（长度与train_losses不匹配）
    loss_details = {
        'reconstruction_loss': [45.0, 40.0],  # 只有2个元素
        'smoothness_loss': [2.0, 2.1, 2.0, 1.9, 1.8, 1.7, 1.6, 1.5, 1.4, 1.3, 
                           1.2, 1.1, 1.0, 0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1],  # 22个元素
        'normal_consistency_loss': [1.5, 1.4, 1.3, 1.2, 1.1],  # 5个元素
        'reflectance_consistency_loss': [1.5, 1.5, 1.7, 2.0, 2.2, 2.3, 2.5, 2.7, 2.9, 3.1]  # 10个元素
    }
    
    try:
        # 测试可视化
        visualizer.visualize_loss_curves(
            train_losses, val_losses, loss_details, epoch=22
        )
        print("✓ 损失曲线可视化修复成功！")
        
    except Exception as e:
        print(f"❌ 损失曲线可视化仍有问题: {e}")
        return False
    
    return True


def test_training_summary():
    """测试训练摘要"""
    print("测试训练摘要...")
    
    visualizer = create_visualizer()
    
    try:
        visualizer.create_training_summary(
            epoch=22,
            train_loss=13.5,
            val_loss=14.2,
            best_val_loss=13.0,
            learning_rate=1e-4
        )
        print("✓ 训练摘要生成成功！")
        
    except Exception as e:
        print(f"❌ 训练摘要生成失败: {e}")
        return False
    
    return True


def main():
    """主测试函数"""
    print("=" * 50)
    print("测试可视化修复")
    print("=" * 50)
    
    success = True
    
    # 测试损失曲线
    if not test_loss_curves_fix():
        success = False
    
    print()
    
    # 测试训练摘要
    if not test_training_summary():
        success = False
    
    print()
    print("=" * 50)
    if success:
        print("🎉 所有可视化功能修复成功！")
        print("现在可以安全地恢复训练了。")
    else:
        print("❌ 仍有问题需要解决。")
    print("=" * 50)


if __name__ == "__main__":
    main()

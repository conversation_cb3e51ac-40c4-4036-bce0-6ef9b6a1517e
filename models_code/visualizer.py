# -*- coding: utf-8 -*-
"""
训练可视化工具 - 实时展示分解结果、光谱图和训练进度
"""

import os
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import torch
from typing import Dict, List, Optional, Tuple
import seaborn as sns
from matplotlib.gridspec import GridSpec
import matplotlib.patches as patches
import platform
import matplotlib.font_manager as fm

from config import *


def setup_english_fonts():
    """
    Setup cross-platform English font support with larger font sizes

    Returns:
        str: Successfully set font name
    """
    # Get current system type
    system = platform.system()

    # English font list for different platforms (by priority)
    font_candidates = {
        'Darwin': [  # macOS
            'Arial',
            'Helvetica',
            'Times New Roman',
            'Calibri',
            'Verdana',
        ],
        'Windows': [  # Windows
            'Arial',
            'Times New Roman',
            'Calibri',
            'Verdana',
            'Tahoma',
        ],
        'Linux': [  # Linux
            'DejaVu Sans',
            'Liberation Sans',
            'Ubuntu',
            'Arial',
            'Helvetica',
        ]
    }

    # Universal fallback fonts
    universal_fonts = [
        'DejaVu Sans',
        'Liberation Sans',
        'sans-serif'
    ]
    
    # Get available system fonts
    available_fonts = [f.name for f in fm.fontManager.ttflist]

    # Find available English fonts by priority
    selected_font = None
    font_list = font_candidates.get(system, []) + universal_fonts

    for font_name in font_list:
        if font_name in available_fonts:
            selected_font = font_name
            break

    # If no suitable font found, try fuzzy matching
    if selected_font is None:
        for font_name in font_list:
            matching_fonts = [f for f in available_fonts if font_name.lower() in f.lower()]
            if matching_fonts:
                selected_font = matching_fonts[0]
                break

    # Set matplotlib font parameters
    if selected_font:
        font_list_to_set = [selected_font] + font_candidates.get(system, []) + universal_fonts
    else:
        font_list_to_set = font_candidates.get(system, []) + universal_fonts
        selected_font = font_list_to_set[0] if font_list_to_set else 'DejaVu Sans'

    # --- Start: Mandatory font settings ---
    plt.rcParams['font.family'] = ['sans-serif']
    plt.rcParams['font.sans-serif'] = font_list_to_set
    plt.rcParams['axes.unicode_minus'] = False  # Fix minus sign display
    plt.rcParams['font.size'] = 16  # Base font size
    plt.rcParams['axes.titlesize'] = 18  # Title font size
    plt.rcParams['axes.labelsize'] = 16  # Axis label font size
    plt.rcParams['xtick.labelsize'] = 14  # X-axis tick label font size
    plt.rcParams['ytick.labelsize'] = 14  # Y-axis tick label font size
    plt.rcParams['legend.fontsize'] = 14  # Legend font size
    plt.rcParams['figure.titlesize'] = 20  # Figure title font size
    # --- End: Mandatory font settings ---

    # Clear matplotlib font cache to ensure new settings take effect
    try:
        matplotlib.font_manager._rebuild()
    except:
        pass

    print(f"Font setup completed - System: {system}, Selected font: {selected_font}")
    print(f"Font list: {font_list_to_set[:3]}...")  # Show only first 3

    return selected_font


class TrainingVisualizer:
    """训练过程可视化器"""
    
    def __init__(self, save_dir: str = "./visualizations"):
        """
        初始化可视化器
        
        Args:
            save_dir: 可视化结果保存目录
        """
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
        # 设置matplotlib样式
        # plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")

        # Setup cross-platform English font support
        self.selected_font = setup_english_fonts()

        # Wavelength array
        self.wavelengths = np.linspace(400, 1000, NUM_BANDS)

        print(f"Training visualizer initialized, save directory: {save_dir}")
    
    def visualize_decomposition_results(self, 
                                      batch: Dict[str, torch.Tensor],
                                      log_reflectance1: torch.Tensor,
                                      log_shading1: torch.Tensor,
                                      epoch: int,
                                      batch_idx: int,
                                      sample_idx: int = 0) -> None:
        """
        可视化分解结果
        
        Args:
            batch: 输入批次数据
            log_reflectance1: 对数域反射率预测
            log_shading1: 对数域阴影预测
            epoch: 当前epoch
            batch_idx: 当前batch索引
            sample_idx: 样本索引
        """
        # 转换为线性域（需要detach以避免梯度计算）
        reflectance = torch.exp(log_reflectance1[sample_idx]).detach().cpu().numpy()  # (C, H, W)
        shading = torch.exp(log_shading1[sample_idx]).detach().cpu().numpy()

        # 获取原始数据
        original_hsi = batch['hsi1'][sample_idx].detach().cpu().numpy()  # (C, H, W)
        log_hsi = batch['log_hsi1'][sample_idx].detach().cpu().numpy()

        # 重建图像
        reconstructed = reflectance * shading
        log_reconstructed = log_reflectance1[sample_idx] + log_shading1[sample_idx]
        reconstructed_from_log = torch.exp(log_reconstructed).detach().cpu().numpy()
        
        # 转换维度 (C, H, W) -> (H, W, C)
        reflectance = reflectance.transpose(1, 2, 0)
        shading = shading.transpose(1, 2, 0)
        original_hsi = original_hsi.transpose(1, 2, 0)
        reconstructed = reconstructed.transpose(1, 2, 0)
        reconstructed_from_log = reconstructed_from_log.transpose(1, 2, 0)
        
        # 创建可视化
        fig = plt.figure(figsize=(20, 12))
        gs = GridSpec(3, 5, figure=fig, hspace=0.3, wspace=0.3)
        
        # 选择代表性波段进行RGB合成
        red_idx = int(NUM_BANDS * (650-400)/(1000-400))    # ~650nm
        green_idx = int(NUM_BANDS * (550-400)/(1000-400))  # ~550nm
        blue_idx = int(NUM_BANDS * (450-400)/(1000-400))   # ~450nm
        
        def create_rgb(hsi_data):
            """创建RGB合成图像"""
            rgb = np.stack([
                hsi_data[:, :, red_idx],
                hsi_data[:, :, green_idx],
                hsi_data[:, :, blue_idx]
            ], axis=2)
            # 归一化到[0,1]
            rgb = (rgb - rgb.min()) / (rgb.max() - rgb.min() + 1e-8)
            return np.clip(rgb, 0, 1)
        
        # First row: RGB composite images
        ax1 = fig.add_subplot(gs[0, 0])
        ax1.imshow(create_rgb(original_hsi))
        ax1.set_title(f'Original Image\nEpoch {epoch}, Batch {batch_idx}')
        ax1.axis('off')

        ax2 = fig.add_subplot(gs[0, 1])
        ax2.imshow(create_rgb(reflectance))
        ax2.set_title('Reflectance (R)')
        ax2.axis('off')

        ax3 = fig.add_subplot(gs[0, 2])
        shading_rgb = create_rgb(shading)
        ax3.imshow(shading_rgb, cmap='gray')
        ax3.set_title('Shading (S)')
        ax3.axis('off')

        ax4 = fig.add_subplot(gs[0, 3])
        ax4.imshow(create_rgb(reconstructed_from_log))
        ax4.set_title('Reconstructed (R*S)')
        ax4.axis('off')

        ax5 = fig.add_subplot(gs[0, 4])
        error_rgb = np.abs(create_rgb(original_hsi) - create_rgb(reconstructed_from_log))
        ax5.imshow(error_rgb)
        ax5.set_title('Reconstruction Error')
        ax5.axis('off')
        
        # 第二行：单波段可视化
        band_indices = [blue_idx, green_idx, red_idx, int(NUM_BANDS*0.8)]
        band_names = ['450nm', '550nm', '650nm', '800nm']
        
        for i, (band_idx, band_name) in enumerate(zip(band_indices, band_names)):
            ax = fig.add_subplot(gs[1, i])
            
            # 创建子图网格
            im_data = np.concatenate([
                original_hsi[:, :, band_idx],
                reflectance[:, :, band_idx],
                shading[:, :, band_idx],
                reconstructed_from_log[:, :, band_idx]
            ], axis=1)
            
            im = ax.imshow(im_data, cmap='viridis')
            ax.set_title(f'{band_name}')
            ax.axis('off')
            
            # 添加分割线
            h, w = original_hsi.shape[:2]
            for j in range(1, 4):
                ax.axvline(x=j*w-0.5, color='white', linewidth=2)
            
            plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
        
        # Second row last: Reconstruction error statistics
        ax = fig.add_subplot(gs[1, 4])
        mse_per_band = np.mean((original_hsi - reconstructed_from_log)**2, axis=(0,1))
        ax.plot(self.wavelengths, mse_per_band, 'r-', linewidth=2)
        ax.set_xlabel('Wavelength (nm)')
        ax.set_ylabel('MSE')
        ax.set_title('Per-band Reconstruction Error')
        ax.grid(True, alpha=0.3)
        
        # 第三行：光谱曲线分析
        # 选择中心区域的像素进行光谱分析
        h, w = original_hsi.shape[:2]
        center_h = slice(h//4, 3*h//4)
        center_w = slice(w//4, 3*w//4)
        
        # 计算平均光谱
        original_spectrum = np.mean(original_hsi[center_h, center_w, :], axis=(0, 1))
        reflectance_spectrum = np.mean(reflectance[center_h, center_w, :], axis=(0, 1))
        shading_spectrum = np.mean(shading[center_h, center_w, :], axis=(0, 1))
        reconstructed_spectrum = np.mean(reconstructed_from_log[center_h, center_w, :], axis=(0, 1))
        
        # Spectral comparison
        ax = fig.add_subplot(gs[2, :2])
        ax.plot(self.wavelengths, original_spectrum, 'b-', linewidth=2, label='Original', alpha=0.8)
        ax.plot(self.wavelengths, reconstructed_spectrum, 'r--', linewidth=2, label='Reconstructed', alpha=0.8)
        ax.fill_between(self.wavelengths, original_spectrum, reconstructed_spectrum,
                       alpha=0.3, color='gray', label='Error Region')
        ax.set_xlabel('Wavelength (nm)')
        ax.set_ylabel('Reflectance')
        ax.set_title('Original vs Reconstructed Spectrum')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # Reflectance spectrum
        ax = fig.add_subplot(gs[2, 2])
        ax.plot(self.wavelengths, reflectance_spectrum, 'g-', linewidth=2)
        ax.set_xlabel('Wavelength (nm)')
        ax.set_ylabel('Reflectance')
        ax.set_title('Intrinsic Reflectance Spectrum')
        ax.grid(True, alpha=0.3)

        # Shading spectrum
        ax = fig.add_subplot(gs[2, 3])
        ax.plot(self.wavelengths, shading_spectrum, 'orange', linewidth=2)
        ax.set_xlabel('Wavelength (nm)')
        ax.set_ylabel('Intensity')
        ax.set_title('Shading/Illumination Spectrum')
        ax.grid(True, alpha=0.3)
        
        # 重建质量指标
        ax = fig.add_subplot(gs[2, 4])
        
        # 计算各种指标
        mse = np.mean((original_hsi - reconstructed_from_log)**2)
        psnr = 20 * np.log10(1.0 / np.sqrt(mse)) if mse > 0 else float('inf')
        
        # 计算光谱角度误差 (SAM)
        def spectral_angle_mapper(spec1, spec2):
            dot_product = np.sum(spec1 * spec2)
            norm1 = np.linalg.norm(spec1)
            norm2 = np.linalg.norm(spec2)
            cos_angle = dot_product / (norm1 * norm2 + 1e-8)
            angle = np.arccos(np.clip(cos_angle, -1, 1))
            return np.degrees(angle)
        
        sam = spectral_angle_mapper(original_spectrum, reconstructed_spectrum)
        
        # 相关系数
        correlation = np.corrcoef(original_spectrum, reconstructed_spectrum)[0, 1]
        
        # Display metrics
        metrics_text = f'Reconstruction Quality:\n\n'
        metrics_text += f'MSE: {mse:.6f}\n'
        metrics_text += f'PSNR: {psnr:.2f} dB\n'
        metrics_text += f'SAM: {sam:.2f}°\n'
        metrics_text += f'Correlation: {correlation:.4f}\n\n'
        metrics_text += f'Data Statistics:\n'
        metrics_text += f'Original range: [{original_hsi.min():.3f}, {original_hsi.max():.3f}]\n'
        metrics_text += f'Reflectance range: [{reflectance.min():.3f}, {reflectance.max():.3f}]\n'
        metrics_text += f'Shading range: [{shading.min():.3f}, {shading.max():.3f}]'
        
        ax.text(0.05, 0.95, metrics_text, transform=ax.transAxes, 
               verticalalignment='top', fontsize=10, 
               bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        ax.axis('off')
        
        # 保存图像
        save_path = os.path.join(self.save_dir, f'decomposition_epoch_{epoch:03d}_batch_{batch_idx:03d}.png')
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"分解结果可视化已保存: {save_path}")
    
    def visualize_loss_curves(self, 
                            train_losses: List[float],
                            val_losses: List[float],
                            loss_details: Dict[str, List[float]],
                            epoch: int) -> None:
        """
        可视化损失曲线
        
        Args:
            train_losses: 训练损失历史
            val_losses: 验证损失历史
            loss_details: 详细损失历史
            epoch: 当前epoch
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        epochs = range(1, len(train_losses) + 1)
        
        # Total loss
        ax = axes[0, 0]
        ax.plot(epochs, train_losses, 'b-', label='Training Loss', linewidth=2)
        if val_losses and len(val_losses) > 0:
            # 确保验证损失的epoch数量正确
            val_epochs = list(range(VAL_FREQUENCY, len(val_losses) * VAL_FREQUENCY + 1, VAL_FREQUENCY))
            # 确保长度匹配
            if len(val_epochs) == len(val_losses):
                ax.plot(val_epochs, val_losses, 'r-', label='Validation Loss', linewidth=2)
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Loss Value')
        ax.set_title('Total Loss Curve')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # Loss decomposition
        ax = axes[0, 1]
        colors = ['red', 'green', 'blue', 'orange', 'purple']
        loss_names = ['reconstruction_loss', 'smoothness_loss', 'normal_consistency_loss', 'reflectance_consistency_loss', 'edge_preserving_loss']
        display_names = ['Reconstruction', 'Smoothness', 'Normal Consistency', 'Reflectance Consistency', 'Edge Preserving']
        
        for i, (loss_name, display_name, color) in enumerate(zip(loss_names, display_names, colors)):
            if loss_name in loss_details and loss_details[loss_name]:
                loss_values = loss_details[loss_name]
                # 确保损失值和epoch数量匹配
                if len(loss_values) == len(epochs):
                    ax.plot(epochs, loss_values, color=color,
                           label=display_name, linewidth=2, alpha=0.8)
        
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Loss Value')
        ax.set_title('Loss Components')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_yscale('log')  # Use log scale

        # Loss proportion
        ax = axes[1, 0]
        if len(train_losses) > 0:
            latest_losses = {}
            for loss_name in loss_names:
                if loss_name in loss_details and loss_details[loss_name]:
                    latest_losses[loss_name] = loss_details[loss_name][-1]

            if latest_losses:
                names = [display_names[i] for i, name in enumerate(loss_names) if name in latest_losses]
                values = [latest_losses[name] for name in loss_names if name in latest_losses]

                _, _, _ = ax.pie(values, labels=names, autopct='%1.1f%%',
                                colors=colors[:len(values)])
                ax.set_title(f'Current Loss Composition (Epoch {epoch})')
        
        # Training progress
        ax = axes[1, 1]
        progress = epoch / NUM_EPOCHS * 100

        # Create progress bar
        _ = ax.barh(0, progress, height=0.3, color='lightblue', alpha=0.7)
        ax.barh(0, 100-progress, left=progress, height=0.3, color='lightgray', alpha=0.3)

        ax.set_xlim(0, 100)
        ax.set_ylim(-0.5, 0.5)
        ax.set_xlabel('Progress (%)')
        ax.set_title(f'Training Progress: {progress:.1f}%')
        ax.set_yticks([])

        # Add text
        ax.text(50, 0, f'{epoch}/{NUM_EPOCHS}', ha='center', va='center',
               fontweight='bold', fontsize=12)
        
        plt.tight_layout()
        
        # 保存图像
        save_path = os.path.join(self.save_dir, f'loss_curves_epoch_{epoch:03d}.png')
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"损失曲线已保存: {save_path}")
    
    def create_training_summary(self, 
                              epoch: int,
                              train_loss: float,
                              val_loss: Optional[float],
                              best_val_loss: float,
                              learning_rate: float) -> None:
        """
        创建训练摘要
        
        Args:
            epoch: 当前epoch
            train_loss: 训练损失
            val_loss: 验证损失
            best_val_loss: 最佳验证损失
            learning_rate: 当前学习率
        """
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.axis('off')
        
        # 创建摘要文本
        val_loss_str = f"{val_loss:.4f}" if val_loss is not None else "N/A"
        summary_text = f"""
        训练摘要 - Epoch {epoch}
        ═══════════════════════════════════════

        📊 当前状态:
        • 训练损失: {train_loss:.4f}
        • 验证损失: {val_loss_str}
        • 最佳验证损失: {best_val_loss:.4f}
        • 学习率: {learning_rate:.2e}
        
        🎯 模型配置:
        • 图像尺寸: {IMG_HEIGHT} × {IMG_WIDTH}
        • 波段数: {NUM_BANDS}
        • 批次大小: {BATCH_SIZE}
        • 设备: {DEVICE}
        
        ⚖️ 损失权重:
        • 重建损失: {LAMBDA_REC}
        • 平滑性损失: {LAMBDA_R_SMOOTH_2D}
        • 法向量一致性: {LAMBDA_S_NORMAL}
        • 反射率一致性: {LAMBDA_R_CONST}
        
        📈 训练进度: {epoch}/{NUM_EPOCHS} ({epoch/NUM_EPOCHS*100:.1f}%)
        """
        
        ax.text(0.05, 0.95, summary_text, transform=ax.transAxes,
               verticalalignment='top', fontsize=12, fontfamily='monospace',
               bbox=dict(boxstyle='round,pad=1', facecolor='lightblue', alpha=0.8))
        
        # 保存摘要
        save_path = os.path.join(self.save_dir, f'training_summary_epoch_{epoch:03d}.png')
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"训练摘要已保存: {save_path}")


def create_visualizer() -> TrainingVisualizer:
    """创建可视化器实例"""
    return TrainingVisualizer(VIS_DIR)

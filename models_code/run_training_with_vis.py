# -*- coding: utf-8 -*-
"""
带可视化的训练脚本 - 实时展示训练过程和分解结果
"""

import os
import torch
from train import Trainer
from config import *


def main():
    """主函数 - 启动带可视化的训练"""
    print("=" * 60)
    print("🚀 启动带可视化的训练")
    print("=" * 60)
    
    # 验证配置
    validate_config()
    
    # 创建训练器
    trainer = Trainer()
    
    # 检查是否有已保存的检查点
    latest_checkpoint = os.path.join(SAVE_DIR, 'latest_checkpoint.pth')
    if os.path.exists(latest_checkpoint):
        print(f"发现检查点文件: {latest_checkpoint}")
        response = input("是否从检查点恢复训练? (y/n): ")
        if response.lower() == 'y':
            trainer.load_checkpoint(latest_checkpoint)
    
    print("\n📊 可视化功能已启用:")
    print(f"  • 分解结果可视化: 每 {VIS_FREQUENCY} 个epoch")
    print(f"  • 损失曲线可视化: 每 {VIS_FREQUENCY} 个epoch")
    print(f"  • 训练摘要: 每 {VIS_FREQUENCY} 个epoch")
    print(f"  • 可视化保存目录: {VIS_DIR}")
    
    print("\n🔗 监控链接:")
    print(f"  • TensorBoard: http://localhost:6007")
    print(f"  • 可视化文件: {os.path.abspath(VIS_DIR)}")
    
    print("\n" + "=" * 60)
    print("开始训练...")
    print("=" * 60)
    
    # 开始训练
    trainer.train()
    
    print("\n" + "=" * 60)
    print("🎉 训练完成！")
    print("=" * 60)
    print(f"📁 结果保存位置:")
    print(f"  • 模型权重: {SAVE_DIR}")
    print(f"  • 训练日志: {LOG_DIR}")
    print(f"  • 可视化结果: {VIS_DIR}")


if __name__ == "__main__":
    main()

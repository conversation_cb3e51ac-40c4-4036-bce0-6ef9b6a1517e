# -*- coding: utf-8 -*-
"""
工具函数 - 数据加载、特征图创建、法向量计算等
"""

import os
import numpy as np
import pandas as pd
import open3d as o3d
from typing import Tuple, List, Optional
import matplotlib.pyplot as plt
from scipy import ndimage


def get_image_dims_from_csv(csv_file_path: str) -> Tuple[int, int]:
    """
    从CSV文件中确定图像维度
    
    Args:
        csv_file_path: CSV文件路径
        
    Returns:
        (height, width): 图像的高度和宽度
    """
    if not os.path.exists(csv_file_path):
        raise FileNotFoundError(f"CSV文件不存在: {csv_file_path}")
    
    # 读取CSV文件
    df = pd.read_csv(csv_file_path)
    
    # 获取行列坐标的最大值
    max_row = df['row'].max()
    max_col = df['col'].max()
    
    # 图像尺寸 = 最大坐标 + 1（因为坐标从0开始）
    height = max_row + 1
    width = max_col + 1
    
    print(f"从{csv_file_path}确定图像尺寸: {height} × {width}")
    
    return height, width


def load_raw_data_from_csv(csv_file_path: str) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """
    从CSV文件加载原始数据
    
    Args:
        csv_file_path: CSV文件路径
        
    Returns:
        pixel_coords: (N, 2) 像素坐标 [row, col]
        point_cloud_data: (N, 3) 点云坐标 [x, y, z]
        normal_data: (N, 3) 法向量数据 [normal_x, normal_y, normal_z]
        hsi_data: (N, NUM_BANDS) 高光谱数据
    """
    if not os.path.exists(csv_file_path):
        raise FileNotFoundError(f"CSV文件不存在: {csv_file_path}")
    
    # 读取CSV文件
    df = pd.read_csv(csv_file_path)
    
    # 提取像素坐标
    pixel_coords = df[['row', 'col']].values.astype(np.int32)
    
    # 提取点云坐标
    point_cloud_data = df[['x', 'y', 'z']].values.astype(np.float32)
    
    # 提取法向量数据
    normal_data = df[['normal_x', 'normal_y', 'normal_z']].values.astype(np.float32)
    
    # 提取高光谱数据
    band_columns = [col for col in df.columns if col.startswith('band_')]
    hsi_data = df[band_columns].values.astype(np.float32)
    
    print(f"从{csv_file_path}加载数据:")
    print(f"  像素数量: {len(pixel_coords)}")
    print(f"  高光谱波段数: {hsi_data.shape[1]}")
    print(f"  点云坐标范围: x[{point_cloud_data[:, 0].min():.2f}, {point_cloud_data[:, 0].max():.2f}], "
          f"y[{point_cloud_data[:, 1].min():.2f}, {point_cloud_data[:, 1].max():.2f}], "
          f"z[{point_cloud_data[:, 2].min():.2f}, {point_cloud_data[:, 2].max():.2f}]")
    
    return pixel_coords, point_cloud_data, normal_data, hsi_data


def create_map_from_points(pixel_coords: np.ndarray, 
                          data_values: np.ndarray, 
                          H: int, W: int, 
                          num_channels: int, 
                          default_val: float = 0.0) -> np.ndarray:
    """
    从稀疏点数据创建密集的特征图
    
    Args:
        pixel_coords: (N, 2) 像素坐标 [row, col]
        data_values: (N, num_channels) 数据值
        H: 图像高度
        W: 图像宽度
        num_channels: 通道数
        default_val: 默认填充值
        
    Returns:
        feature_map: (H, W, num_channels) 特征图
    """
    # 初始化特征图
    feature_map = np.full((H, W, num_channels), default_val, dtype=np.float32)
    
    # 确保坐标在有效范围内
    valid_mask = (pixel_coords[:, 0] >= 0) & (pixel_coords[:, 0] < H) & \
                 (pixel_coords[:, 1] >= 0) & (pixel_coords[:, 1] < W)
    
    valid_coords = pixel_coords[valid_mask]
    valid_data = data_values[valid_mask]
    
    # 填充特征图
    if len(valid_coords) > 0:
        feature_map[valid_coords[:, 0], valid_coords[:, 1], :] = valid_data
    
    print(f"创建特征图: {feature_map.shape}, 有效像素: {len(valid_coords)}/{len(pixel_coords)}")
    
    return feature_map


def calculate_normals_open3d(point_cloud_data: np.ndarray, 
                           radius: float, 
                           max_nn: int) -> np.ndarray:
    """
    使用Open3D计算点云法向量
    
    Args:
        point_cloud_data: (N, 3) 点云坐标
        radius: 法向量估计半径
        max_nn: 最大邻居数
        
    Returns:
        normals: (N, 3) 法向量数组
    """
    # 创建Open3D点云对象
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(point_cloud_data)
    
    # 估计法向量
    pcd.estimate_normals(
        search_param=o3d.geometry.KDTreeSearchParamHybrid(radius=radius, max_nn=max_nn)
    )
    
    # 尝试统一法向量方向（指向外侧）
    try:
        # 计算点云中心
        center = np.mean(point_cloud_data, axis=0)
        pcd.orient_normals_to_align_with_direction(orientation_reference=center)
        print("成功统一法向量方向")
    except Exception as e:
        print(f"法向量方向统一失败，使用原始方向: {e}")
    
    # 提取法向量
    normals = np.asarray(pcd.normals).astype(np.float32)
    
    print(f"计算法向量: {normals.shape}, 半径={radius}, 最大邻居数={max_nn}")
    
    return normals


def calculate_r_init_from_45_degree(data_dir: str, 
                                   angles: List[int], 
                                   H: int, W: int, B: int, 
                                   roi_ratio: float = 0.2) -> Tuple[np.ndarray, np.ndarray]:
    """
    使用45°角度的中心区域平均值作为所有角度的R_init参考值
    
    Args:
        data_dir: 数据目录路径
        angles: 所有角度列表
        H, W, B: 图像高度、宽度、波段数
        roi_ratio: 中心区域比例
        
    Returns:
        r_init: (H, W, B) 的参考反射率图像
        reference_spectrum: (B,) 参考光谱
    """
    # 确保45度在角度列表中
    if 45 not in angles:
        raise ValueError("45度角度数据不存在于提供的角度列表中")
    
    # 加载45度的数据
    csv_path_45 = os.path.join(data_dir, "hsi_point_fusion_45_with_normals_oriented.csv")
    
    if not os.path.exists(csv_path_45):
        raise FileNotFoundError(f"找不到45度数据文件: {csv_path_45}")
    
    # 读取45度数据
    pixel_coords, _, _, hsi_data = load_raw_data_from_csv(csv_path_45)
    
    # 创建45度的高光谱图像
    hsi_image_45 = create_map_from_points(pixel_coords, hsi_data, H, W, B)
    
    # 计算中心区域的边界
    center_h_start = int(H * (1 - roi_ratio) / 2)
    center_h_end = int(H * (1 + roi_ratio) / 2)
    center_w_start = int(W * (1 - roi_ratio) / 2)
    center_w_end = int(W * (1 + roi_ratio) / 2)
    
    # 提取中心区域
    center_region = hsi_image_45[center_h_start:center_h_end, 
                                 center_w_start:center_w_end, :]
    
    # 计算中心区域的平均光谱（排除零值像素）
    valid_mask = np.sum(center_region, axis=2) > 0
    
    if np.sum(valid_mask) == 0:
        raise ValueError("45度数据的中心区域没有有效像素")
    
    # 计算有效像素的平均光谱
    valid_pixels = center_region[valid_mask]
    reference_spectrum = np.mean(valid_pixels, axis=0)
    
    print(f"从45度数据中提取的参考光谱统计:")
    print(f"  中心区域大小: {center_h_end-center_h_start} × {center_w_end-center_w_start}")
    print(f"  有效像素数量: {np.sum(valid_mask)}")
    print(f"  参考光谱范围: [{np.min(reference_spectrum):.2f}, {np.max(reference_spectrum):.2f}]")
    print(f"  参考光谱均值: {np.mean(reference_spectrum):.2f}")
    
    # 创建R_init图像：将参考光谱广播到整个图像
    r_init = np.tile(reference_spectrum.reshape(1, 1, B), (H, W, 1))
    
    return r_init, reference_spectrum


def validate_r_init_quality(reference_spectrum: np.ndarray) -> None:
    """
    验证R_init的质量
    
    Args:
        reference_spectrum: (B,) 参考光谱
    """
    print("\n=== R_init质量验证 ===")
    
    # 1. 检查参考光谱的合理性
    print(f"参考光谱统计:")
    print(f"  最小值: {np.min(reference_spectrum):.2f}")
    print(f"  最大值: {np.max(reference_spectrum):.2f}")
    print(f"  均值: {np.mean(reference_spectrum):.2f}")
    print(f"  标准差: {np.std(reference_spectrum):.2f}")
    
    # 2. 检查光谱形状的合理性
    # 对于苹果，通常在红光区域（~650-700nm）有较高反射率
    B = len(reference_spectrum)
    red_region_start = int(B * (650-400)/(1000-400))  # 大约对应650nm
    red_region_end = int(B * (700-400)/(1000-400))    # 大约对应700nm
    green_region_start = int(B * (500-400)/(1000-400))  # 大约对应500nm
    green_region_end = int(B * (600-400)/(1000-400))    # 大约对应600nm
    
    red_reflectance = np.mean(reference_spectrum[red_region_start:red_region_end])
    green_reflectance = np.mean(reference_spectrum[green_region_start:green_region_end])
    
    print(f"\n光谱特征分析:")
    print(f"  红光区域平均反射率: {red_reflectance:.2f}")
    print(f"  绿光区域平均反射率: {green_reflectance:.2f}")
    print(f"  红绿比值: {red_reflectance/green_reflectance:.2f}")
    
    if red_reflectance > green_reflectance:
        print("  ✓ 符合苹果的典型光谱特征（红光反射率高于绿光）")
    else:
        print("  ⚠ 光谱特征可能异常，请检查数据")


def smooth_feature_map(feature_map: np.ndarray, sigma: float = 1.0) -> np.ndarray:
    """
    对特征图进行平滑处理
    
    Args:
        feature_map: (H, W, C) 特征图
        sigma: 高斯滤波的标准差
        
    Returns:
        smoothed_map: 平滑后的特征图
    """
    smoothed_map = feature_map.copy()
    
    for c in range(feature_map.shape[2]):
        smoothed_map[:, :, c] = ndimage.gaussian_filter(feature_map[:, :, c], sigma=sigma)
    
    return smoothed_map


def visualize_spectrum(spectrum: np.ndarray, 
                      title: str = "光谱曲线", 
                      save_path: Optional[str] = None) -> None:
    """
    可视化光谱曲线
    
    Args:
        spectrum: (B,) 光谱数据
        title: 图表标题
        save_path: 保存路径（可选）
    """
    wavelengths = np.linspace(400, 1000, len(spectrum))
    
    plt.figure(figsize=(10, 6))
    plt.plot(wavelengths, spectrum, 'b-', linewidth=2)
    plt.xlabel('波长 (nm)')
    plt.ylabel('反射率')
    plt.title(title)
    plt.grid(True, alpha=0.3)
    plt.xlim(400, 1000)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"光谱图保存到: {save_path}")
    
    plt.show()


def create_visualization_grid(images: List[np.ndarray], 
                            titles: List[str], 
                            save_path: Optional[str] = None) -> None:
    """
    创建图像网格可视化
    
    Args:
        images: 图像列表
        titles: 标题列表
        save_path: 保存路径（可选）
    """
    n_images = len(images)
    cols = min(4, n_images)
    rows = (n_images + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=(4*cols, 4*rows))
    if rows == 1:
        axes = [axes] if cols == 1 else axes
    else:
        axes = axes.flatten()
    
    for i, (img, title) in enumerate(zip(images, titles)):
        if len(img.shape) == 3:
            # 如果是多通道图像，显示第一个通道或RGB合成
            if img.shape[2] == 3:
                axes[i].imshow(img)
            else:
                axes[i].imshow(img[:, :, 0], cmap='viridis')
        else:
            axes[i].imshow(img, cmap='viridis')
        
        axes[i].set_title(title)
        axes[i].axis('off')
    
    # 隐藏多余的子图
    for i in range(n_images, len(axes)):
        axes[i].axis('off')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"可视化网格保存到: {save_path}")
    
    plt.show()

# 基于多模态数据与物理先验的苹果本征高光谱特性提取模型

## 项目概述

本项目实现了一个深度学习模型，用于从苹果的多视角高光谱图像和对应的三维点云数据中，分解出苹果的本征反射率（光照不变的"真实颜色"）和光照/阴影分量。

## 核心创新

### 1. 统一参考标准
- **45°参考策略**: 使用45°角度中心区域的平均光谱作为所有角度的R_init参考值
- **避免镜面反射**: 45°角度能有效避免镜面反射的影响，提供可靠的材质参考
- **物理合理性**: 基于光学原理选择最佳参考角度

### 2. 多模态深度融合
- **高光谱信息**: 224个波段的光谱特征 (400-1000nm)
- **几何信息**: 三维点云坐标和表面法向量
- **物理先验**: 基于Lambert反射模型的约束
- **空间一致性**: 利用邻域信息的平滑性约束

### 3. 双网络架构
- **RES网络**: 反射率估计子网络 (Reflectance Estimation Subnetwork)
- **SES网络**: 阴影估计子网络 (Shading Estimation Subnetwork)
- **U-Net骨干**: 编码器-解码器结构，保留多尺度特征
- **跳跃连接**: 融合不同层次的特征信息

## 技术架构

### 数据流程
```
多视角HSI数据 → 特征图构建 → 网络输入 → 双网络预测 → 物理约束优化 → 本征分解结果
     ↓              ↓           ↓          ↓            ↓              ↓
  CSV文件     [HSI,Normal,    对数域     [log_R,     重建约束      [R,S,重建图像]
             Coord,R_init]    处理      log_S]      一致性约束
```

### 损失函数设计
```python
L_total = λ_rec × L_reconstruction +           # 重建约束: I = R ⊙ S
          λ_smooth × L_reflectance_smoothness + # 反射率平滑性
          λ_normal × L_shading_normal +         # 阴影-法向量一致性  
          λ_const × L_reflectance_consistency   # 多视角一致性
```

## 快速开始

### 1. 环境安装
```bash
# 基础依赖
pip install torch torchvision numpy pandas matplotlib
pip install open3d scipy tqdm tensorboard

# 或使用requirements.txt (如果提供)
pip install -r requirements.txt
```

### 2. 数据准备
确保数据目录结构如下：
```
fusion_data/
├── hsi_point_fusion_35_with_normals_oriented.csv
├── hsi_point_fusion_40_with_normals_oriented.csv
├── hsi_point_fusion_45_with_normals_oriented.csv  # 必需：参考角度
├── hsi_point_fusion_50_with_normals_oriented.csv
├── hsi_point_fusion_55_with_normals_oriented.csv
├── hsi_point_fusion_60_with_normals_oriented.csv
├── hsi_point_fusion_65_with_normals_oriented.csv
└── hsi_point_fusion_70_with_normals_oriented.csv
```

### 3. 运行流程

#### 检查环境
```bash
python main.py check-env
```

#### 测试数据加载
```bash
python main.py test-all
```

#### 训练模型
```bash
python main.py train
```

#### 运行推理
```bash
python main.py inference
```

## 详细使用指南

### 配置文件 (config.py)
主要配置参数：
```python
# 数据配置
DATA_DIR = "/path/to/fusion_data"
ANGLES = [35, 40, 45, 50, 55, 60, 65, 70]
REFERENCE_ANGLE = 45

# 训练配置
BATCH_SIZE = 2
LEARNING_RATE = 1e-4
NUM_EPOCHS = 100

# 损失权重
LAMBDA_REC = 1.0          # 重建损失
LAMBDA_R_SMOOTH_2D = 0.1  # 平滑性损失
LAMBDA_S_NORMAL = 0.1     # 法向量一致性
LAMBDA_R_CONST = 0.5      # 反射率一致性
```

### 数据格式说明
每个CSV文件包含以下列：
- **空间信息**: `row`, `col`, `x`, `y`, `z` (5列)
- **法向量**: `normal_x`, `normal_y`, `normal_z`, `normal_angle_with_z`, `normal_orientation_xy`, `normal_inclination` (6列)  
- **高光谱**: `band_0` 到 `band_223` (224列，对应400-1000nm)

### 训练监控
- **TensorBoard**: 实时监控训练过程
  ```bash
  tensorboard --logdir=./logs
  ```
- **检查点**: 自动保存最佳模型和训练状态
- **可视化**: 定期保存训练过程中的可视化结果

### 推理结果
推理完成后会生成：
1. **RGB合成图像**: 原始、反射率、阴影、重建图像对比
2. **光谱曲线**: 各组分的光谱特征分析
3. **几何信息**: 法向量和表面曲率可视化
4. **分解对比**: 不同波段的分解效果
5. **一致性分析**: 多视角反射率一致性评估

## 项目结构

```
models_code/
├── main.py              # 主运行脚本
├── config.py            # 配置文件
├── utils.py             # 工具函数 (数据加载、可视化等)
├── dataset.py           # 数据集类 (多视角数据加载)
├── model.py             # 网络模型 (双U-Net架构)
├── loss.py              # 损失函数 (物理约束损失)
├── train.py             # 训练脚本
├── inference.py         # 推理和可视化脚本
└── README.md            # 项目说明

# 运行时生成的目录
saved_models/            # 模型检查点
logs/                    # TensorBoard日志
visualizations/          # 训练过程可视化
inference_results/       # 推理结果
```

## 核心算法

### R_init计算策略
```python
def calculate_r_init_from_45_degree():
    """
    使用45°角度中心区域平均值作为参考反射率
    - 选择45°避免镜面反射
    - 提取中心20%×20%区域
    - 计算有效像素的平均光谱
    - 广播到所有像素位置
    """
```

### 物理约束实现
1. **重建约束**: 在对数域确保 `log(I) = log(R) + log(S)`
2. **平滑性约束**: 最小化反射率的空间梯度
3. **法向量一致性**: 阴影变化与法向量变化相关
4. **多视角一致性**: 不同视角的反射率应该相同

### 网络设计亮点
- **多尺度特征**: U-Net的跳跃连接保留细节
- **对数域处理**: 提高数值稳定性
- **批归一化**: 加速收敛和提高稳定性
- **梯度裁剪**: 防止梯度爆炸

## 实验结果与分析

### 预期性能指标
1. **重建精度**: PSNR > 25dB, SSIM > 0.8
2. **光谱保真度**: 光谱角度误差 < 5°
3. **多视角一致性**: 反射率相关系数 > 0.9
4. **物理合理性**: 分解结果符合Lambert模型

### 可视化分析
- **RGB合成**: 直观展示分解效果
- **光谱曲线**: 验证苹果典型光谱特征
- **一致性热图**: 多视角反射率相关性
- **误差分析**: 重建误差的空间分布

## 故障排除

### 常见问题
1. **内存不足**: 减小batch_size或图像尺寸
2. **收敛困难**: 调整学习率和损失权重
3. **数据加载错误**: 检查CSV文件格式和路径
4. **GPU不可用**: 自动回退到CPU训练

### 调试技巧
```bash
# 检查数据
python main.py test-data

# 检查模型
python main.py test-model

# 检查环境
python main.py check-env
```

```bash
# 1. 检查环境和数据
python main.py check-env

# 2. 测试所有组件
python main.py test-all

# 3. 训练模型
python main.py train

# 4. 运行推理
python main.py inference
```

## 扩展应用

### 1. 材质分析
- 基于本征反射率进行材质分类
- 苹果品种识别和质量评估
- 表面缺陷检测

### 2. 光照建模
- 环境光照条件估计
- 光照变化模拟
- 虚拟光照重打光

### 3. 3D重建增强
- 结合几何和光谱的三维重建
- 纹理映射优化
- 多模态数据融合

## 参考文献与致谢

本项目基于以下理论基础：
- Intrinsic Image Decomposition
- Lambert Reflectance Model  
- Multi-view Consistency
- Hyperspectral Image Analysis

## 许可证

[在此添加许可证信息]

## 联系方式

[在此添加联系方式]
